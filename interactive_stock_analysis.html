
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>增强型股票趋势分析</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .info {
            margin: 10px 0;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 3px;
            font-family: monospace;
        }
        .trading-table {
            margin-top: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .trading-table h3 {
            margin: 0;
            padding: 15px 20px;
            background-color: #007bff;
            color: white;
            font-size: 16px;
        }
        .table-container {
            max-height: 400px;
            overflow-y: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        tr:hover {
            background-color: #f8f9fa;
        }
        .buy-signal {
            color: #28a745;
            font-weight: bold;
        }
        .sell-signal {
            color: #dc3545;
            font-weight: bold;
        }
        .profit-positive {
            color: #28a745;
            font-weight: bold;
        }
        .profit-negative {
            color: #dc3545;
            font-weight: bold;
        }
        .no-trades {
            text-align: center;
            padding: 40px;
            color: #6c757d;
            font-style: italic;
        }
        .pair-badge {
            background-color: #007bff;
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: bold;
        }
        .pair-info {
            font-size: 12px;
            color: #6c757d;
        }
        .open-position {
            background-color: #fff3cd;
        }
        .closed-position {
            background-color: #f8f9fa;
        }
        .open-position:hover {
            background-color: #ffeaa7;
        }
        .closed-position:hover {
            background-color: #e9ecef;
        }
        #chart {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .stats {
            display: flex;
            justify-content: space-around;
            margin: 15px 0;
        }
        .stat-item {
            text-align: center;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📈 增强型股票趋势分析 (波动阈值: 10.0%)</h1>

        <div class="controls">
            <button class="btn" onclick="addNextDay()" id="nextBtn">添加下一个交易日</button>
            <button class="btn" onclick="resetChart()">重置图表</button>
            <button class="btn" onclick="autoPlay()" id="autoBtn">自动播放</button>
            <button class="btn" onclick="stopAuto()" id="stopBtn" disabled>停止播放</button>
        </div>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="currentDay">0</div>
                <div class="stat-label">当前交易日</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="totalDays">1102</div>
                <div class="stat-label">总交易日</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="highSlope">--</div>
                <div class="stat-label">高点趋势斜率</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="lowSlope">--</div>
                <div class="stat-label">低点趋势斜率</div>
            </div>
        </div>

        <div class="info" id="info">
            点击"添加下一个交易日"开始分析，或点击"自动播放"观看完整过程
        </div>

        <div id="chart"></div>

        <!-- 交易明细表格 -->
        <div class="trading-table">
            <h3>📊 交易明细记录</h3>
            <div class="table-container">
                <table id="tradingTable">
                    <thead>
                        <tr>
                            <th>交易对</th>
                            <th>序号</th>
                            <th>日期</th>
                            <th>交易类型</th>
                            <th>价格</th>
                            <th>数量</th>
                            <th>金额</th>
                            <th>配对信息</th>
                            <th>盈亏</th>
                            <th>盈亏率</th>
                            <th>持有天数</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody id="tradingTableBody">
                        <tr>
                            <td colspan="12" class="no-trades">暂无交易记录，开始添加数据后将显示交易信号</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // 增强的转折点检测器 (JS版本)
        class EnhancedTurnPointDetector {
            constructor(percentChangeThreshold = 10.0) {
                this.threshold = percentChangeThreshold / 100.0;
                this.records = [];
                this.turn_points = [];
                this.last_extreme_price = null;
                this.last_extreme_point_info = null; // {date, price, dayIndex}
                this.current_trend = 0; // 0: undefined, 1: up, -1: down
                this.temp_first_point_high_info = null;
                this.temp_first_point_low_info = null;
            }

            addRecord(date, high, low, dayIndex) {
                this.records.push({ date, high, low, dayIndex });

                if (this.current_trend === 0) {
                    if (!this.temp_first_point_high_info) {
                        this.temp_first_point_high_info = { date, price: high, dayIndex };
                        this.temp_first_point_low_info = { date, price: low, dayIndex };
                    } else {
                        const firstHigh = this.temp_first_point_high_info.price;

                        if (high > firstHigh * (1 + this.threshold)) {
                            this.current_trend = 1; // UP
                            this.turn_points.push({
                                date: this.temp_first_point_low_info.date,
                                price: this.temp_first_point_low_info.price,
                                pointType: 'TROUGH',
                                dayIndex: this.temp_first_point_low_info.dayIndex
                            });
                            this.last_extreme_price = high;
                            this.last_extreme_point_info = { date, price: high, dayIndex };
                        } else if (low < firstHigh * (1 - this.threshold)) {
                            this.current_trend = -1; // DOWN
                            this.turn_points.push({
                                date: this.temp_first_point_high_info.date,
                                price: this.temp_first_point_high_info.price,
                                pointType: 'PEAK',
                                dayIndex: this.temp_first_point_high_info.dayIndex
                            });
                            this.last_extreme_price = low;
                            this.last_extreme_point_info = { date, price: low, dayIndex };
                        } else {
                            if (high > this.temp_first_point_high_info.price) {
                                this.temp_first_point_high_info = { date, price: high, dayIndex };
                            }
                            if (low < this.temp_first_point_low_info.price) {
                                this.temp_first_point_low_info = { date, price: low, dayIndex };
                            }
                        }
                    }
                } else if (this.current_trend === 1) { // UP trend
                    if (high > this.last_extreme_price) {
                        this.last_extreme_price = high;
                        this.last_extreme_point_info = { date, price: high, dayIndex };
                    } else if (low < this.last_extreme_price * (1 - this.threshold)) {
                        const peak = {
                            date: this.last_extreme_point_info.date,
                            price: this.last_extreme_point_info.price,
                            pointType: 'PEAK',
                            dayIndex: this.last_extreme_point_info.dayIndex
                        };
                        if (this.turn_points.length === 0 || peak.price > this.turn_points[this.turn_points.length - 1].price) {
                            this.turn_points.push(peak);
                            this.current_trend = -1; // DOWN
                            this.last_extreme_price = low;
                            this.last_extreme_point_info = { date, price: low, dayIndex };
                        }
                    }
                } else if (this.current_trend === -1) { // DOWN trend
                    if (low < this.last_extreme_price) {
                        this.last_extreme_price = low;
                        this.last_extreme_point_info = { date, price: low, dayIndex };
                    } else if (high > this.last_extreme_price * (1 + this.threshold)) {
                        const trough = {
                            date: this.last_extreme_point_info.date,
                            price: this.last_extreme_point_info.price,
                            pointType: 'TROUGH',
                            dayIndex: this.last_extreme_point_info.dayIndex
                        };
                        if (this.turn_points.length === 0 || trough.price < this.turn_points[this.turn_points.length - 1].price) {
                            this.turn_points.push(trough);
                            this.current_trend = 1; // UP
                            this.last_extreme_price = high;
                            this.last_extreme_point_info = { date, price: high, dayIndex };
                        }
                    }
                }
            }
            getPeaks() { return this.turn_points.filter(p => p.pointType === 'PEAK'); }
            getTroughs() { return this.turn_points.filter(t => t.pointType === 'TROUGH'); }
        }

        // 交易策略类（JavaScript版本）
        class SmartTradingStrategy {
            constructor(buyThreshold = 8.0, sellThreshold = 15.0) {
                this.buyThreshold = buyThreshold / 100.0;
                this.sellThreshold = sellThreshold / 100.0;
                this.signals = [];
            }

            analyzeSignals(calculator) {
                // 不清空历史信号，只添加新的信号
                const peaks = calculator.turnPointDetector.getPeaks();
                const troughs = calculator.turnPointDetector.getTroughs();

                // 生成买入信号 - 上升趋势中的波谷
                for (let trough of troughs) {
                    // 检查是否已经存在该信号
                    const existingSignal = this.signals.find(s =>
                        s.dayIndex === trough.dayIndex && s.signalType === 'BUY'
                    );

                    if (!existingSignal && this.isUptrendBuyOpportunity(trough, calculator)) {
                        this.signals.push({
                            date: trough.date,
                            price: trough.price,
                            signalType: 'BUY',
                            dayIndex: trough.dayIndex,
                            reason: '上升趋势波谷买入',
                            confidence: 0.8
                        });
                    }
                }

                // 生成卖出信号 - 高位获利了结
                for (let peak of peaks) {
                    // 检查是否已经存在该信号
                    const existingSignal = this.signals.find(s =>
                        s.dayIndex === peak.dayIndex && s.signalType === 'SELL'
                    );

                    if (!existingSignal && this.isProfitTakingOpportunity(peak, calculator)) {
                        this.signals.push({
                            date: peak.date,
                            price: peak.price,
                            signalType: 'SELL',
                            dayIndex: peak.dayIndex,
                            reason: '高位获利了结',
                            confidence: 0.8
                        });
                    }
                }

                return this.signals;
            }

            isUptrendBuyOpportunity(trough, calculator) {
                // 简化版：检查是否为上升趋势中的波谷
                const troughs = calculator.turnPointDetector.getTroughs();
                const troughIndex = troughs.findIndex(t => t.dayIndex === trough.dayIndex);
                if (troughIndex > 0) {
                    return trough.price > troughs[troughIndex - 1].price;
                }
                return false;
            }

            isProfitTakingOpportunity(peak, calculator) {
                // 检查涨幅是否足够
                const troughs = calculator.turnPointDetector.getTroughs();
                let prevTrough = null;
                // 不修改原数组，使用slice()创建副本再反转
                for (let trough of troughs.slice().reverse()) {
                    if (trough.dayIndex < peak.dayIndex) {
                        prevTrough = trough;
                        break;
                    }
                }
                if (prevTrough) {
                    const gainRatio = (peak.price - prevTrough.price) / prevTrough.price;
                    return gainRatio >= this.sellThreshold;
                }
                return false;
            }

            getBuySignals() { return this.signals.filter(s => s.signalType === 'BUY'); }
            getSellSignals() { return this.signals.filter(s => s.signalType === 'SELL'); }
        }

        // 趋势线计算器类（JavaScript版本）
        class StockTrendCalculator {
            constructor(windowSize, turnPointThreshold, enableTrading = true) {
                this.windowSize = windowSize;
                this.records = [];
                this.dayCounter = 0;
                this.turnPointDetector = new EnhancedTurnPointDetector(turnPointThreshold);
                this.tradingStrategy = enableTrading ? new SmartTradingStrategy(8.0, 15.0) : null;
            }

            addRecord(timestamp, high, low) {
                const record = {
                    date: timestamp,
                    high: high,
                    low: low,
                    dayIndex: this.dayCounter
                };
                this.records.push(record);
                this.turnPointDetector.addRecord(timestamp, high, low, this.dayCounter);
                this.dayCounter++;
                if (this.records.length > this.windowSize) {
                    this.records.shift();
                }
            }

            getCurrentTrendLines() {
                const peaks = this.turnPointDetector.getPeaks();
                const troughs = this.turnPointDetector.getTroughs();
                const currentStartDay = Math.max(0, this.dayCounter - this.windowSize);
                const recentPeaks = peaks.filter(p => p.dayIndex >= currentStartDay);
                const recentTroughs = troughs.filter(t => t.dayIndex >= currentStartDay);
                
                let highLine = null;
                if (recentPeaks.length >= 2) {
                    const peakPoints = recentPeaks.map(p => [p.dayIndex, p.price]);
                    highLine = this.fitWeightedLine(peakPoints);
                }

                let lowLine = null;
                if (recentTroughs.length >= 2) {
                    const troughPoints = recentTroughs.map(t => [t.dayIndex, t.price]);
                    lowLine = this.fitWeightedLine(troughPoints);
                }
                return [highLine, lowLine];
            }

            fitWeightedLine(points) {
                if (points.length < 2) return null;
                const n = points.length;
                const xValues = points.map(p => p[0]);
                const yValues = points.map(p => p[1]);
                const decayFactor = 0.05;
                const weights = Array.from({length: n}, (_, i) => Math.exp(-(n - 1 - i) * decayFactor));
                const sumW = weights.reduce((a, b) => a + b, 0);
                const sumWx = weights.reduce((sum, w, i) => sum + w * xValues[i], 0);
                const sumWy = weights.reduce((sum, w, i) => sum + w * yValues[i], 0);
                const sumWxx = weights.reduce((sum, w, i) => sum + w * xValues[i] * xValues[i], 0);
                const sumWxy = weights.reduce((sum, w, i) => sum + w * xValues[i] * yValues[i], 0);
                const denominator = sumW * sumWxx - sumWx * sumWx;
                let slope, intercept;
                if (Math.abs(denominator) < 1e-10) {
                    slope = 0;
                    intercept = sumWy / sumW;
                } else {
                    slope = (sumW * sumWxy - sumWx * sumWy) / denominator;
                    intercept = (sumWy * sumWxx - sumWx * sumWxy) / denominator;
                }
                return {
                    slope: slope,
                    intercept: intercept,
                    startDay: Math.min(...xValues),
                    endDay: Math.max(...xValues)
                };
            }

            getTrendLineValues(lineParams, dayIndices) {
                if (!lineParams) return dayIndices.map(() => null);
                return dayIndices.map(day => lineParams.slope * day + lineParams.intercept);
            }

            getTradingSignals() {
                if (!this.tradingStrategy) return [];
                return this.tradingStrategy.analyzeSignals(this);
            }

            getBuySignals() {
                if (!this.tradingStrategy) return [];
                return this.tradingStrategy.getBuySignals();
            }

            getSellSignals() {
                if (!this.tradingStrategy) return [];
                return this.tradingStrategy.getSellSignals();
            }
        }

        // 股票数据和初始化
        const stockData = [
  {
    "date": "2020-12-04",
    "open": 38.85,
    "close": 46.9,
    "high": 46.9,
    "low": 38.85,
    "index": 0
  },
  {
    "date": "2020-12-07",
    "open": 51.65,
    "close": 42.07,
    "high": 51.65,
    "low": 42.07,
    "index": 1
  },
  {
    "date": "2020-12-08",
    "open": 38.35,
    "close": 40.31,
    "high": 41.69,
    "low": 37.73,
    "index": 2
  },
  {
    "date": "2020-12-09",
    "open": 38.93,
    "close": 38.3,
    "high": 39.53,
    "low": 38.17,
    "index": 3
  },
  {
    "date": "2020-12-10",
    "open": 37.9,
    "close": 35.96,
    "high": 37.9,
    "low": 35.93,
    "index": 4
  },
  {
    "date": "2020-12-11",
    "open": 34.89,
    "close": 35.14,
    "high": 36.64,
    "low": 34.89,
    "index": 5
  },
  {
    "date": "2020-12-14",
    "open": 35.23,
    "close": 33.58,
    "high": 35.57,
    "low": 33.45,
    "index": 6
  },
  {
    "date": "2020-12-15",
    "open": 33.46,
    "close": 33.73,
    "high": 34.16,
    "low": 33.2,
    "index": 7
  },
  {
    "date": "2020-12-16",
    "open": 33.54,
    "close": 33.14,
    "high": 33.93,
    "low": 33.14,
    "index": 8
  },
  {
    "date": "2020-12-17",
    "open": 33.14,
    "close": 34.43,
    "high": 34.75,
    "low": 33.13,
    "index": 9
  },
  {
    "date": "2020-12-18",
    "open": 34.1,
    "close": 33.43,
    "high": 34.42,
    "low": 33.43,
    "index": 10
  },
  {
    "date": "2020-12-21",
    "open": 33.64,
    "close": 33.62,
    "high": 33.97,
    "low": 33.01,
    "index": 11
  },
  {
    "date": "2020-12-22",
    "open": 33.3,
    "close": 32.61,
    "high": 33.44,
    "low": 32.59,
    "index": 12
  },
  {
    "date": "2020-12-23",
    "open": 32.71,
    "close": 32.31,
    "high": 32.9,
    "low": 32.23,
    "index": 13
  },
  {
    "date": "2020-12-24",
    "open": 32.23,
    "close": 30.39,
    "high": 32.24,
    "low": 30.36,
    "index": 14
  },
  {
    "date": "2020-12-25",
    "open": 29.86,
    "close": 29.91,
    "high": 30.31,
    "low": 29.49,
    "index": 15
  },
  {
    "date": "2020-12-28",
    "open": 29.86,
    "close": 28.46,
    "high": 30.09,
    "low": 28.44,
    "index": 16
  },
  {
    "date": "2020-12-29",
    "open": 28.26,
    "close": 28.32,
    "high": 29.18,
    "low": 27.77,
    "index": 17
  },
  {
    "date": "2020-12-30",
    "open": 28.19,
    "close": 29.84,
    "high": 30.06,
    "low": 28.02,
    "index": 18
  },
  {
    "date": "2020-12-31",
    "open": 29.92,
    "close": 30.36,
    "high": 31.33,
    "low": 29.92,
    "index": 19
  },
  {
    "date": "2021-01-04",
    "open": 30.31,
    "close": 30.39,
    "high": 30.8,
    "low": 29.68,
    "index": 20
  },
  {
    "date": "2021-01-05",
    "open": 30.39,
    "close": 31.01,
    "high": 31.33,
    "low": 29.98,
    "index": 21
  },
  {
    "date": "2021-01-06",
    "open": 31.02,
    "close": 31.93,
    "high": 32.76,
    "low": 31.02,
    "index": 22
  },
  {
    "date": "2021-01-07",
    "open": 31.65,
    "close": 30.41,
    "high": 31.65,
    "low": 30.31,
    "index": 23
  },
  {
    "date": "2021-01-08",
    "open": 30.27,
    "close": 31.13,
    "high": 31.33,
    "low": 29.27,
    "index": 24
  },
  {
    "date": "2021-01-11",
    "open": 31.33,
    "close": 30.39,
    "high": 31.33,
    "low": 30.32,
    "index": 25
  },
  {
    "date": "2021-01-12",
    "open": 30.09,
    "close": 29.66,
    "high": 30.52,
    "low": 29.58,
    "index": 26
  },
  {
    "date": "2021-01-13",
    "open": 29.85,
    "close": 28.5,
    "high": 29.86,
    "low": 28.38,
    "index": 27
  },
  {
    "date": "2021-01-14",
    "open": 28.48,
    "close": 29.05,
    "high": 29.27,
    "low": 28.08,
    "index": 28
  },
  {
    "date": "2021-01-15",
    "open": 29.02,
    "close": 30.13,
    "high": 30.26,
    "low": 28.86,
    "index": 29
  },
  {
    "date": "2021-01-18",
    "open": 30.14,
    "close": 30.46,
    "high": 30.7,
    "low": 29.82,
    "index": 30
  },
  {
    "date": "2021-01-19",
    "open": 30.46,
    "close": 30.38,
    "high": 30.97,
    "low": 30.18,
    "index": 31
  },
  {
    "date": "2021-01-20",
    "open": 30.53,
    "close": 30.76,
    "high": 31.01,
    "low": 30.38,
    "index": 32
  },
  {
    "date": "2021-01-21",
    "open": 30.65,
    "close": 29.93,
    "high": 30.66,
    "low": 29.87,
    "index": 33
  },
  {
    "date": "2021-01-22",
    "open": 29.42,
    "close": 28.61,
    "high": 29.42,
    "low": 28.58,
    "index": 34
  },
  {
    "date": "2021-01-25",
    "open": 28.61,
    "close": 30.81,
    "high": 30.97,
    "low": 28.57,
    "index": 35
  },
  {
    "date": "2021-01-26",
    "open": 30.31,
    "close": 30.61,
    "high": 31.51,
    "low": 30.31,
    "index": 36
  },
  {
    "date": "2021-01-27",
    "open": 30.22,
    "close": 31.43,
    "high": 31.51,
    "low": 30.22,
    "index": 37
  },
  {
    "date": "2021-01-28",
    "open": 31.02,
    "close": 30.58,
    "high": 31.89,
    "low": 30.41,
    "index": 38
  },
  {
    "date": "2021-01-29",
    "open": 30.58,
    "close": 29.92,
    "high": 31.01,
    "low": 29.64,
    "index": 39
  },
  {
    "date": "2021-02-01",
    "open": 29.77,
    "close": 30.17,
    "high": 30.29,
    "low": 29.27,
    "index": 40
  },
  {
    "date": "2021-02-02",
    "open": 30.31,
    "close": 32.32,
    "high": 33.21,
    "low": 30.31,
    "index": 41
  },
  {
    "date": "2021-02-03",
    "open": 32.09,
    "close": 30.91,
    "high": 32.43,
    "low": 30.89,
    "index": 42
  },
  {
    "date": "2021-02-04",
    "open": 30.86,
    "close": 31.56,
    "high": 31.87,
    "low": 29.77,
    "index": 43
  },
  {
    "date": "2021-02-05",
    "open": 31.24,
    "close": 29.45,
    "high": 31.64,
    "low": 29.42,
    "index": 44
  },
  {
    "date": "2021-02-08",
    "open": 29.42,
    "close": 27.08,
    "high": 29.42,
    "low": 26.74,
    "index": 45
  },
  {
    "date": "2021-02-09",
    "open": 26.85,
    "close": 27.11,
    "high": 27.42,
    "low": 26.75,
    "index": 46
  },
  {
    "date": "2021-02-10",
    "open": 27.16,
    "close": 27.54,
    "high": 28.07,
    "low": 27.14,
    "index": 47
  },
  {
    "date": "2021-02-18",
    "open": 27.86,
    "close": 28.72,
    "high": 28.87,
    "low": 27.63,
    "index": 48
  },
  {
    "date": "2021-02-19",
    "open": 28.73,
    "close": 28.87,
    "high": 29.02,
    "low": 28.14,
    "index": 49
  },
  {
    "date": "2021-02-22",
    "open": 28.97,
    "close": 28.77,
    "high": 29.32,
    "low": 28.62,
    "index": 50
  },
  {
    "date": "2021-02-23",
    "open": 28.73,
    "close": 29.29,
    "high": 29.41,
    "low": 28.52,
    "index": 51
  },
  {
    "date": "2021-02-24",
    "open": 29.28,
    "close": 28.93,
    "high": 29.33,
    "low": 28.77,
    "index": 52
  },
  {
    "date": "2021-02-25",
    "open": 28.86,
    "close": 28.14,
    "high": 29.05,
    "low": 28.08,
    "index": 53
  },
  {
    "date": "2021-02-26",
    "open": 28.13,
    "close": 28.35,
    "high": 28.64,
    "low": 28.12,
    "index": 54
  },
  {
    "date": "2021-03-01",
    "open": 28.56,
    "close": 29.02,
    "high": 29.13,
    "low": 28.37,
    "index": 55
  },
  {
    "date": "2021-03-02",
    "open": 28.97,
    "close": 28.55,
    "high": 29.03,
    "low": 28.42,
    "index": 56
  },
  {
    "date": "2021-03-03",
    "open": 28.44,
    "close": 28.63,
    "high": 28.75,
    "low": 28.32,
    "index": 57
  },
  {
    "date": "2021-03-04",
    "open": 28.53,
    "close": 28.12,
    "high": 28.73,
    "low": 28.1,
    "index": 58
  },
  {
    "date": "2021-03-05",
    "open": 28.12,
    "close": 28.2,
    "high": 28.33,
    "low": 27.9,
    "index": 59
  },
  {
    "date": "2021-03-08",
    "open": 28.31,
    "close": 28.53,
    "high": 28.86,
    "low": 28.31,
    "index": 60
  },
  {
    "date": "2021-03-09",
    "open": 28.79,
    "close": 29.56,
    "high": 30.06,
    "low": 28.26,
    "index": 61
  },
  {
    "date": "2021-03-10",
    "open": 29.62,
    "close": 29.19,
    "high": 29.82,
    "low": 28.57,
    "index": 62
  },
  {
    "date": "2021-03-11",
    "open": 29.14,
    "close": 30.71,
    "high": 30.76,
    "low": 28.89,
    "index": 63
  },
  {
    "date": "2021-03-12",
    "open": 31.07,
    "close": 31.1,
    "high": 31.28,
    "low": 30.14,
    "index": 64
  },
  {
    "date": "2021-03-15",
    "open": 31.43,
    "close": 31.21,
    "high": 32.1,
    "low": 30.77,
    "index": 65
  },
  {
    "date": "2021-03-16",
    "open": 30.8,
    "close": 31.22,
    "high": 31.54,
    "low": 30.34,
    "index": 66
  },
  {
    "date": "2021-03-17",
    "open": 30.76,
    "close": 31.72,
    "high": 31.95,
    "low": 30.76,
    "index": 67
  },
  {
    "date": "2021-03-18",
    "open": 31.76,
    "close": 32.91,
    "high": 34.68,
    "low": 31.75,
    "index": 68
  },
  {
    "date": "2021-03-19",
    "open": 32.99,
    "close": 33.56,
    "high": 34.45,
    "low": 32.94,
    "index": 69
  },
  {
    "date": "2021-03-22",
    "open": 33.4,
    "close": 33.43,
    "high": 33.84,
    "low": 32.54,
    "index": 70
  },
  {
    "date": "2021-03-23",
    "open": 33.12,
    "close": 34.24,
    "high": 36.02,
    "low": 32.9,
    "index": 71
  },
  {
    "date": "2021-03-24",
    "open": 33.85,
    "close": 33.43,
    "high": 34.89,
    "low": 33.23,
    "index": 72
  },
  {
    "date": "2021-03-25",
    "open": 33.04,
    "close": 33.1,
    "high": 33.69,
    "low": 32.77,
    "index": 73
  },
  {
    "date": "2021-03-26",
    "open": 33.21,
    "close": 34.22,
    "high": 34.44,
    "low": 33.08,
    "index": 74
  },
  {
    "date": "2021-03-29",
    "open": 34.17,
    "close": 35.52,
    "high": 35.88,
    "low": 33.98,
    "index": 75
  },
  {
    "date": "2021-03-30",
    "open": 35.52,
    "close": 34.64,
    "high": 36.05,
    "low": 34.13,
    "index": 76
  },
  {
    "date": "2021-03-31",
    "open": 34.26,
    "close": 34.78,
    "high": 35.22,
    "low": 33.56,
    "index": 77
  },
  {
    "date": "2021-04-01",
    "open": 34.51,
    "close": 33.28,
    "high": 34.73,
    "low": 32.99,
    "index": 78
  },
  {
    "date": "2021-04-02",
    "open": 33.66,
    "close": 33.73,
    "high": 34.26,
    "low": 32.81,
    "index": 79
  },
  {
    "date": "2021-04-06",
    "open": 33.44,
    "close": 32.72,
    "high": 33.73,
    "low": 32.63,
    "index": 80
  },
  {
    "date": "2021-04-07",
    "open": 32.96,
    "close": 31.88,
    "high": 33.03,
    "low": 31.43,
    "index": 81
  },
  {
    "date": "2021-04-08",
    "open": 31.92,
    "close": 31.67,
    "high": 32.33,
    "low": 31.6,
    "index": 82
  },
  {
    "date": "2021-04-09",
    "open": 31.67,
    "close": 32.01,
    "high": 32.2,
    "low": 31.43,
    "index": 83
  },
  {
    "date": "2021-04-12",
    "open": 32.09,
    "close": 31.57,
    "high": 32.49,
    "low": 31.43,
    "index": 84
  },
  {
    "date": "2021-04-13",
    "open": 31.45,
    "close": 31.17,
    "high": 31.89,
    "low": 31.08,
    "index": 85
  },
  {
    "date": "2021-04-14",
    "open": 31.17,
    "close": 31.31,
    "high": 31.43,
    "low": 30.73,
    "index": 86
  },
  {
    "date": "2021-04-15",
    "open": 31.27,
    "close": 31.48,
    "high": 31.88,
    "low": 31.02,
    "index": 87
  },
  {
    "date": "2021-04-16",
    "open": 31.43,
    "close": 32.14,
    "high": 32.37,
    "low": 31.12,
    "index": 88
  },
  {
    "date": "2021-04-19",
    "open": 32.69,
    "close": 34.15,
    "high": 34.47,
    "low": 32.68,
    "index": 89
  },
  {
    "date": "2021-04-20",
    "open": 34.1,
    "close": 33.97,
    "high": 35.35,
    "low": 33.84,
    "index": 90
  },
  {
    "date": "2021-04-21",
    "open": 32.93,
    "close": 33.81,
    "high": 34.41,
    "low": 32.37,
    "index": 91
  },
  {
    "date": "2021-04-22",
    "open": 34.33,
    "close": 35.66,
    "high": 35.71,
    "low": 34.11,
    "index": 92
  },
  {
    "date": "2021-04-23",
    "open": 35.66,
    "close": 35.63,
    "high": 36.76,
    "low": 35.44,
    "index": 93
  },
  {
    "date": "2021-04-26",
    "open": 35.62,
    "close": 36.49,
    "high": 36.81,
    "low": 35.62,
    "index": 94
  },
  {
    "date": "2021-04-27",
    "open": 36.5,
    "close": 36.45,
    "high": 36.78,
    "low": 35.57,
    "index": 95
  },
  {
    "date": "2021-04-28",
    "open": 36.45,
    "close": 34.91,
    "high": 36.65,
    "low": 34.11,
    "index": 96
  },
  {
    "date": "2021-04-29",
    "open": 35.2,
    "close": 36.69,
    "high": 36.77,
    "low": 34.33,
    "index": 97
  },
  {
    "date": "2021-04-30",
    "open": 36.38,
    "close": 37.85,
    "high": 37.89,
    "low": 36.14,
    "index": 98
  },
  {
    "date": "2021-05-06",
    "open": 37.81,
    "close": 39.55,
    "high": 41.01,
    "low": 37.31,
    "index": 99
  },
  {
    "date": "2021-05-07",
    "open": 38.8,
    "close": 37.74,
    "high": 39.38,
    "low": 37.68,
    "index": 100
  },
  {
    "date": "2021-05-10",
    "open": 37.9,
    "close": 38.5,
    "high": 38.61,
    "low": 36.56,
    "index": 101
  },
  {
    "date": "2021-05-11",
    "open": 38.35,
    "close": 37.05,
    "high": 39.63,
    "low": 36.84,
    "index": 102
  },
  {
    "date": "2021-05-12",
    "open": 37.1,
    "close": 38.12,
    "high": 38.17,
    "low": 36.6,
    "index": 103
  },
  {
    "date": "2021-05-13",
    "open": 37.55,
    "close": 38.39,
    "high": 38.47,
    "low": 36.78,
    "index": 104
  },
  {
    "date": "2021-05-14",
    "open": 38.77,
    "close": 37.89,
    "high": 38.77,
    "low": 37.37,
    "index": 105
  },
  {
    "date": "2021-05-17",
    "open": 37.45,
    "close": 40.34,
    "high": 40.9,
    "low": 36.92,
    "index": 106
  },
  {
    "date": "2021-05-18",
    "open": 40.03,
    "close": 39.49,
    "high": 40.9,
    "low": 39.07,
    "index": 107
  },
  {
    "date": "2021-05-19",
    "open": 39.1,
    "close": 41.5,
    "high": 42.06,
    "low": 38.87,
    "index": 108
  },
  {
    "date": "2021-05-20",
    "open": 42.16,
    "close": 41.31,
    "high": 42.24,
    "low": 40.64,
    "index": 109
  },
  {
    "date": "2021-05-21",
    "open": 42.13,
    "close": 39.94,
    "high": 42.13,
    "low": 39.79,
    "index": 110
  },
  {
    "date": "2021-05-24",
    "open": 39.53,
    "close": 40.9,
    "high": 41.45,
    "low": 39.33,
    "index": 111
  },
  {
    "date": "2021-05-25",
    "open": 40.23,
    "close": 39.54,
    "high": 41.56,
    "low": 39.46,
    "index": 112
  },
  {
    "date": "2021-05-26",
    "open": 39.64,
    "close": 40.84,
    "high": 41.17,
    "low": 39.13,
    "index": 113
  },
  {
    "date": "2021-05-27",
    "open": 40.84,
    "close": 42.63,
    "high": 43.1,
    "low": 40.24,
    "index": 114
  },
  {
    "date": "2021-05-28",
    "open": 42.63,
    "close": 43.39,
    "high": 44.37,
    "low": 41.96,
    "index": 115
  },
  {
    "date": "2021-05-31",
    "open": 43.31,
    "close": 44.05,
    "high": 44.19,
    "low": 42.63,
    "index": 116
  },
  {
    "date": "2021-06-01",
    "open": 44.07,
    "close": 43.64,
    "high": 44.69,
    "low": 43.34,
    "index": 117
  },
  {
    "date": "2021-06-02",
    "open": 43.72,
    "close": 42.34,
    "high": 43.88,
    "low": 42.2,
    "index": 118
  },
  {
    "date": "2021-06-03",
    "open": 42.46,
    "close": 42.32,
    "high": 43.34,
    "low": 42.19,
    "index": 119
  },
  {
    "date": "2021-06-04",
    "open": 42.17,
    "close": 41.83,
    "high": 43.12,
    "low": 40.84,
    "index": 120
  },
  {
    "date": "2021-06-07",
    "open": 42.0,
    "close": 44.49,
    "high": 44.69,
    "low": 41.89,
    "index": 121
  },
  {
    "date": "2021-06-08",
    "open": 44.77,
    "close": 43.42,
    "high": 45.84,
    "low": 42.96,
    "index": 122
  },
  {
    "date": "2021-06-09",
    "open": 43.49,
    "close": 43.96,
    "high": 44.42,
    "low": 42.78,
    "index": 123
  },
  {
    "date": "2021-06-10",
    "open": 43.95,
    "close": 42.92,
    "high": 44.1,
    "low": 42.51,
    "index": 124
  },
  {
    "date": "2021-06-11",
    "open": 42.84,
    "close": 40.62,
    "high": 42.89,
    "low": 40.27,
    "index": 125
  },
  {
    "date": "2021-06-15",
    "open": 41.39,
    "close": 42.59,
    "high": 42.94,
    "low": 40.54,
    "index": 126
  },
  {
    "date": "2021-06-16",
    "open": 42.7,
    "close": 41.92,
    "high": 43.1,
    "low": 41.02,
    "index": 127
  },
  {
    "date": "2021-06-17",
    "open": 41.93,
    "close": 43.22,
    "high": 43.41,
    "low": 41.6,
    "index": 128
  },
  {
    "date": "2021-06-18",
    "open": 43.23,
    "close": 42.63,
    "high": 43.7,
    "low": 42.12,
    "index": 129
  },
  {
    "date": "2021-06-21",
    "open": 42.49,
    "close": 44.72,
    "high": 45.12,
    "low": 42.21,
    "index": 130
  },
  {
    "date": "2021-06-22",
    "open": 44.77,
    "close": 45.07,
    "high": 45.82,
    "low": 44.26,
    "index": 131
  },
  {
    "date": "2021-06-23",
    "open": 45.21,
    "close": 46.56,
    "high": 47.06,
    "low": 44.64,
    "index": 132
  },
  {
    "date": "2021-06-24",
    "open": 46.57,
    "close": 46.57,
    "high": 47.84,
    "low": 46.11,
    "index": 133
  },
  {
    "date": "2021-06-25",
    "open": 46.62,
    "close": 46.31,
    "high": 47.27,
    "low": 45.64,
    "index": 134
  },
  {
    "date": "2021-06-28",
    "open": 46.27,
    "close": 47.88,
    "high": 47.99,
    "low": 45.26,
    "index": 135
  },
  {
    "date": "2021-06-29",
    "open": 47.99,
    "close": 46.49,
    "high": 47.99,
    "low": 44.51,
    "index": 136
  },
  {
    "date": "2021-06-30",
    "open": 46.11,
    "close": 42.69,
    "high": 46.13,
    "low": 42.34,
    "index": 137
  },
  {
    "date": "2021-07-01",
    "open": 43.61,
    "close": 43.07,
    "high": 43.96,
    "low": 42.63,
    "index": 138
  },
  {
    "date": "2021-07-02",
    "open": 42.99,
    "close": 42.02,
    "high": 42.99,
    "low": 41.2,
    "index": 139
  },
  {
    "date": "2021-07-05",
    "open": 40.55,
    "close": 41.12,
    "high": 41.91,
    "low": 38.44,
    "index": 140
  },
  {
    "date": "2021-07-06",
    "open": 40.84,
    "close": 39.87,
    "high": 41.48,
    "low": 38.2,
    "index": 141
  },
  {
    "date": "2021-07-07",
    "open": 39.77,
    "close": 37.84,
    "high": 40.15,
    "low": 37.7,
    "index": 142
  },
  {
    "date": "2021-07-08",
    "open": 37.44,
    "close": 38.38,
    "high": 38.7,
    "low": 36.3,
    "index": 143
  },
  {
    "date": "2021-07-09",
    "open": 38.17,
    "close": 38.9,
    "high": 39.04,
    "low": 37.49,
    "index": 144
  },
  {
    "date": "2021-07-12",
    "open": 39.56,
    "close": 39.23,
    "high": 39.56,
    "low": 38.34,
    "index": 145
  },
  {
    "date": "2021-07-13",
    "open": 39.42,
    "close": 41.56,
    "high": 41.97,
    "low": 38.56,
    "index": 146
  },
  {
    "date": "2021-07-14",
    "open": 41.51,
    "close": 41.42,
    "high": 42.19,
    "low": 41.06,
    "index": 147
  },
  {
    "date": "2021-07-15",
    "open": 41.58,
    "close": 41.77,
    "high": 42.52,
    "low": 40.84,
    "index": 148
  },
  {
    "date": "2021-07-16",
    "open": 41.92,
    "close": 41.85,
    "high": 42.99,
    "low": 40.86,
    "index": 149
  },
  {
    "date": "2021-07-19",
    "open": 41.85,
    "close": 43.78,
    "high": 44.05,
    "low": 41.32,
    "index": 150
  },
  {
    "date": "2021-07-20",
    "open": 43.26,
    "close": 43.84,
    "high": 44.03,
    "low": 43.26,
    "index": 151
  },
  {
    "date": "2021-07-21",
    "open": 44.24,
    "close": 47.89,
    "high": 48.26,
    "low": 43.92,
    "index": 152
  },
  {
    "date": "2021-07-22",
    "open": 47.97,
    "close": 48.25,
    "high": 50.17,
    "low": 47.63,
    "index": 153
  },
  {
    "date": "2021-07-23",
    "open": 48.79,
    "close": 47.52,
    "high": 49.31,
    "low": 47.19,
    "index": 154
  },
  {
    "date": "2021-07-26",
    "open": 47.06,
    "close": 47.24,
    "high": 48.45,
    "low": 46.42,
    "index": 155
  },
  {
    "date": "2021-07-27",
    "open": 47.23,
    "close": 46.66,
    "high": 49.4,
    "low": 46.27,
    "index": 156
  },
  {
    "date": "2021-07-28",
    "open": 46.58,
    "close": 46.2,
    "high": 47.12,
    "low": 44.07,
    "index": 157
  },
  {
    "date": "2021-07-29",
    "open": 47.83,
    "close": 48.91,
    "high": 49.19,
    "low": 46.82,
    "index": 158
  },
  {
    "date": "2021-07-30",
    "open": 47.92,
    "close": 48.89,
    "high": 49.47,
    "low": 47.92,
    "index": 159
  },
  {
    "date": "2021-08-02",
    "open": 48.9,
    "close": 45.82,
    "high": 48.98,
    "low": 45.63,
    "index": 160
  },
  {
    "date": "2021-08-03",
    "open": 45.9,
    "close": 46.92,
    "high": 49.11,
    "low": 45.82,
    "index": 161
  },
  {
    "date": "2021-08-04",
    "open": 46.92,
    "close": 51.7,
    "high": 51.7,
    "low": 46.67,
    "index": 162
  },
  {
    "date": "2021-08-05",
    "open": 53.7,
    "close": 52.63,
    "high": 55.84,
    "low": 51.74,
    "index": 163
  },
  {
    "date": "2021-08-06",
    "open": 53.69,
    "close": 52.09,
    "high": 55.33,
    "low": 51.4,
    "index": 164
  },
  {
    "date": "2021-08-09",
    "open": 53.18,
    "close": 52.67,
    "high": 53.18,
    "low": 50.12,
    "index": 165
  },
  {
    "date": "2021-08-10",
    "open": 52.63,
    "close": 54.1,
    "high": 54.42,
    "low": 51.2,
    "index": 166
  },
  {
    "date": "2021-08-11",
    "open": 53.34,
    "close": 53.33,
    "high": 54.42,
    "low": 51.92,
    "index": 167
  },
  {
    "date": "2021-08-12",
    "open": 53.74,
    "close": 55.67,
    "high": 56.26,
    "low": 53.2,
    "index": 168
  },
  {
    "date": "2021-08-13",
    "open": 55.09,
    "close": 57.27,
    "high": 58.12,
    "low": 55.04,
    "index": 169
  },
  {
    "date": "2021-08-16",
    "open": 57.12,
    "close": 56.2,
    "high": 57.12,
    "low": 55.33,
    "index": 170
  },
  {
    "date": "2021-08-17",
    "open": 56.2,
    "close": 54.77,
    "high": 56.2,
    "low": 53.9,
    "index": 171
  },
  {
    "date": "2021-08-18",
    "open": 54.89,
    "close": 54.47,
    "high": 55.34,
    "low": 52.93,
    "index": 172
  },
  {
    "date": "2021-08-19",
    "open": 55.06,
    "close": 55.84,
    "high": 57.99,
    "low": 54.79,
    "index": 173
  },
  {
    "date": "2021-08-20",
    "open": 55.65,
    "close": 55.49,
    "high": 56.22,
    "low": 54.11,
    "index": 174
  },
  {
    "date": "2021-08-23",
    "open": 54.9,
    "close": 57.83,
    "high": 57.9,
    "low": 54.85,
    "index": 175
  },
  {
    "date": "2021-08-24",
    "open": 59.01,
    "close": 57.12,
    "high": 60.13,
    "low": 56.37,
    "index": 176
  },
  {
    "date": "2021-08-25",
    "open": 57.24,
    "close": 57.72,
    "high": 58.08,
    "low": 55.96,
    "index": 177
  },
  {
    "date": "2021-08-26",
    "open": 57.49,
    "close": 57.63,
    "high": 58.24,
    "low": 57.06,
    "index": 178
  },
  {
    "date": "2021-08-27",
    "open": 57.34,
    "close": 55.4,
    "high": 57.34,
    "low": 51.9,
    "index": 179
  },
  {
    "date": "2021-08-30",
    "open": 54.77,
    "close": 50.27,
    "high": 54.99,
    "low": 49.77,
    "index": 180
  },
  {
    "date": "2021-08-31",
    "open": 49.88,
    "close": 46.42,
    "high": 49.91,
    "low": 45.96,
    "index": 181
  },
  {
    "date": "2021-09-01",
    "open": 46.47,
    "close": 48.72,
    "high": 48.72,
    "low": 44.09,
    "index": 182
  },
  {
    "date": "2021-09-02",
    "open": 47.84,
    "close": 47.54,
    "high": 48.54,
    "low": 45.92,
    "index": 183
  },
  {
    "date": "2021-09-03",
    "open": 47.84,
    "close": 45.04,
    "high": 47.84,
    "low": 44.55,
    "index": 184
  },
  {
    "date": "2021-09-06",
    "open": 45.13,
    "close": 47.98,
    "high": 48.39,
    "low": 44.37,
    "index": 185
  },
  {
    "date": "2021-09-07",
    "open": 47.99,
    "close": 47.4,
    "high": 48.34,
    "low": 46.82,
    "index": 186
  },
  {
    "date": "2021-09-08",
    "open": 47.4,
    "close": 49.06,
    "high": 49.42,
    "low": 46.44,
    "index": 187
  },
  {
    "date": "2021-09-09",
    "open": 49.07,
    "close": 49.74,
    "high": 52.25,
    "low": 49.06,
    "index": 188
  },
  {
    "date": "2021-09-10",
    "open": 48.85,
    "close": 49.8,
    "high": 50.27,
    "low": 47.81,
    "index": 189
  },
  {
    "date": "2021-09-13",
    "open": 49.8,
    "close": 49.02,
    "high": 50.13,
    "low": 48.09,
    "index": 190
  },
  {
    "date": "2021-09-14",
    "open": 48.79,
    "close": 49.13,
    "high": 50.84,
    "low": 48.79,
    "index": 191
  },
  {
    "date": "2021-09-15",
    "open": 48.42,
    "close": 46.2,
    "high": 48.42,
    "low": 46.19,
    "index": 192
  },
  {
    "date": "2021-09-16",
    "open": 46.19,
    "close": 44.77,
    "high": 47.17,
    "low": 44.63,
    "index": 193
  },
  {
    "date": "2021-09-17",
    "open": 44.77,
    "close": 44.81,
    "high": 45.84,
    "low": 43.32,
    "index": 194
  },
  {
    "date": "2021-09-22",
    "open": 44.53,
    "close": 47.86,
    "high": 49.06,
    "low": 43.58,
    "index": 195
  },
  {
    "date": "2021-09-23",
    "open": 48.04,
    "close": 46.55,
    "high": 48.77,
    "low": 46.37,
    "index": 196
  },
  {
    "date": "2021-09-24",
    "open": 46.57,
    "close": 45.13,
    "high": 47.12,
    "low": 44.82,
    "index": 197
  },
  {
    "date": "2021-09-27",
    "open": 45.49,
    "close": 43.94,
    "high": 45.69,
    "low": 43.34,
    "index": 198
  },
  {
    "date": "2021-09-28",
    "open": 43.94,
    "close": 43.47,
    "high": 45.09,
    "low": 43.34,
    "index": 199
  },
  {
    "date": "2021-09-29",
    "open": 43.92,
    "close": 41.92,
    "high": 43.99,
    "low": 41.83,
    "index": 200
  },
  {
    "date": "2021-09-30",
    "open": 41.97,
    "close": 41.98,
    "high": 42.84,
    "low": 41.2,
    "index": 201
  },
  {
    "date": "2021-10-08",
    "open": 42.07,
    "close": 40.92,
    "high": 42.52,
    "low": 40.14,
    "index": 202
  },
  {
    "date": "2021-10-11",
    "open": 40.54,
    "close": 40.49,
    "high": 41.34,
    "low": 39.77,
    "index": 203
  },
  {
    "date": "2021-10-12",
    "open": 40.59,
    "close": 38.28,
    "high": 40.59,
    "low": 37.54,
    "index": 204
  },
  {
    "date": "2021-10-13",
    "open": 38.67,
    "close": 41.03,
    "high": 41.89,
    "low": 38.34,
    "index": 205
  },
  {
    "date": "2021-10-14",
    "open": 41.09,
    "close": 41.87,
    "high": 41.92,
    "low": 40.62,
    "index": 206
  },
  {
    "date": "2021-10-15",
    "open": 41.87,
    "close": 40.04,
    "high": 42.11,
    "low": 39.81,
    "index": 207
  },
  {
    "date": "2021-10-18",
    "open": 40.04,
    "close": 40.63,
    "high": 40.82,
    "low": 39.06,
    "index": 208
  },
  {
    "date": "2021-10-19",
    "open": 40.63,
    "close": 40.56,
    "high": 41.44,
    "low": 40.01,
    "index": 209
  },
  {
    "date": "2021-10-20",
    "open": 40.56,
    "close": 42.45,
    "high": 42.88,
    "low": 39.92,
    "index": 210
  },
  {
    "date": "2021-10-21",
    "open": 42.45,
    "close": 43.14,
    "high": 43.69,
    "low": 41.23,
    "index": 211
  },
  {
    "date": "2021-10-22",
    "open": 42.65,
    "close": 43.42,
    "high": 43.56,
    "low": 41.84,
    "index": 212
  },
  {
    "date": "2021-10-25",
    "open": 43.0,
    "close": 43.07,
    "high": 43.95,
    "low": 41.92,
    "index": 213
  },
  {
    "date": "2021-10-26",
    "open": 43.07,
    "close": 42.05,
    "high": 43.07,
    "low": 40.99,
    "index": 214
  },
  {
    "date": "2021-10-27",
    "open": 42.13,
    "close": 40.57,
    "high": 42.14,
    "low": 40.17,
    "index": 215
  },
  {
    "date": "2021-10-28",
    "open": 40.13,
    "close": 39.14,
    "high": 40.91,
    "low": 39.06,
    "index": 216
  },
  {
    "date": "2021-10-29",
    "open": 39.15,
    "close": 41.83,
    "high": 42.27,
    "low": 39.12,
    "index": 217
  },
  {
    "date": "2021-11-01",
    "open": 42.37,
    "close": 45.07,
    "high": 46.06,
    "low": 40.95,
    "index": 218
  },
  {
    "date": "2021-11-02",
    "open": 45.24,
    "close": 45.31,
    "high": 46.13,
    "low": 44.59,
    "index": 219
  },
  {
    "date": "2021-11-03",
    "open": 45.15,
    "close": 45.13,
    "high": 45.92,
    "low": 44.06,
    "index": 220
  },
  {
    "date": "2021-11-04",
    "open": 45.13,
    "close": 49.74,
    "high": 49.74,
    "low": 44.77,
    "index": 221
  },
  {
    "date": "2021-11-05",
    "open": 49.75,
    "close": 49.27,
    "high": 51.84,
    "low": 49.06,
    "index": 222
  },
  {
    "date": "2021-11-08",
    "open": 49.9,
    "close": 50.81,
    "high": 51.04,
    "low": 48.52,
    "index": 223
  },
  {
    "date": "2021-11-09",
    "open": 50.81,
    "close": 50.74,
    "high": 51.42,
    "low": 50.06,
    "index": 224
  },
  {
    "date": "2021-11-10",
    "open": 50.39,
    "close": 52.08,
    "high": 52.63,
    "low": 50.04,
    "index": 225
  },
  {
    "date": "2021-11-11",
    "open": 52.07,
    "close": 51.76,
    "high": 53.14,
    "low": 50.92,
    "index": 226
  },
  {
    "date": "2021-11-12",
    "open": 51.48,
    "close": 52.17,
    "high": 53.34,
    "low": 51.02,
    "index": 227
  },
  {
    "date": "2021-11-15",
    "open": 52.29,
    "close": 54.78,
    "high": 54.84,
    "low": 51.83,
    "index": 228
  },
  {
    "date": "2021-11-16",
    "open": 55.05,
    "close": 52.45,
    "high": 55.05,
    "low": 51.2,
    "index": 229
  },
  {
    "date": "2021-11-17",
    "open": 52.27,
    "close": 50.48,
    "high": 52.77,
    "low": 49.77,
    "index": 230
  },
  {
    "date": "2021-11-18",
    "open": 50.24,
    "close": 49.05,
    "high": 51.44,
    "low": 48.78,
    "index": 231
  },
  {
    "date": "2021-11-19",
    "open": 48.64,
    "close": 49.06,
    "high": 51.38,
    "low": 48.64,
    "index": 232
  },
  {
    "date": "2021-11-22",
    "open": 48.72,
    "close": 49.02,
    "high": 49.69,
    "low": 48.42,
    "index": 233
  },
  {
    "date": "2021-11-23",
    "open": 50.41,
    "close": 54.01,
    "high": 54.01,
    "low": 49.92,
    "index": 234
  },
  {
    "date": "2021-11-24",
    "open": 52.81,
    "close": 51.06,
    "high": 53.69,
    "low": 50.49,
    "index": 235
  },
  {
    "date": "2021-11-25",
    "open": 50.99,
    "close": 53.49,
    "high": 54.63,
    "low": 49.11,
    "index": 236
  },
  {
    "date": "2021-11-26",
    "open": 53.77,
    "close": 52.63,
    "high": 54.06,
    "low": 51.92,
    "index": 237
  },
  {
    "date": "2021-11-29",
    "open": 52.27,
    "close": 52.49,
    "high": 54.32,
    "low": 51.42,
    "index": 238
  },
  {
    "date": "2021-11-30",
    "open": 54.06,
    "close": 51.64,
    "high": 54.06,
    "low": 51.12,
    "index": 239
  },
  {
    "date": "2021-12-01",
    "open": 52.25,
    "close": 54.59,
    "high": 54.98,
    "low": 50.17,
    "index": 240
  },
  {
    "date": "2021-12-02",
    "open": 54.06,
    "close": 55.07,
    "high": 55.38,
    "low": 53.12,
    "index": 241
  },
  {
    "date": "2021-12-03",
    "open": 54.99,
    "close": 53.5,
    "high": 56.69,
    "low": 52.88,
    "index": 242
  },
  {
    "date": "2021-12-06",
    "open": 53.51,
    "close": 54.24,
    "high": 54.6,
    "low": 52.39,
    "index": 243
  },
  {
    "date": "2021-12-07",
    "open": 54.5,
    "close": 53.21,
    "high": 55.03,
    "low": 50.56,
    "index": 244
  },
  {
    "date": "2021-12-08",
    "open": 53.49,
    "close": 55.49,
    "high": 56.13,
    "low": 52.43,
    "index": 245
  },
  {
    "date": "2021-12-09",
    "open": 56.27,
    "close": 54.97,
    "high": 57.63,
    "low": 54.92,
    "index": 246
  },
  {
    "date": "2021-12-10",
    "open": 54.39,
    "close": 55.76,
    "high": 55.94,
    "low": 54.15,
    "index": 247
  },
  {
    "date": "2021-12-13",
    "open": 55.11,
    "close": 55.84,
    "high": 57.04,
    "low": 54.42,
    "index": 248
  },
  {
    "date": "2021-12-14",
    "open": 55.83,
    "close": 56.38,
    "high": 56.81,
    "low": 54.59,
    "index": 249
  },
  {
    "date": "2021-12-15",
    "open": 56.22,
    "close": 55.43,
    "high": 56.42,
    "low": 55.24,
    "index": 250
  },
  {
    "date": "2021-12-16",
    "open": 56.2,
    "close": 54.34,
    "high": 56.2,
    "low": 53.7,
    "index": 251
  },
  {
    "date": "2021-12-17",
    "open": 54.24,
    "close": 52.43,
    "high": 54.46,
    "low": 51.2,
    "index": 252
  },
  {
    "date": "2021-12-20",
    "open": 52.23,
    "close": 51.64,
    "high": 52.81,
    "low": 49.47,
    "index": 253
  },
  {
    "date": "2021-12-21",
    "open": 51.09,
    "close": 50.84,
    "high": 51.64,
    "low": 49.77,
    "index": 254
  },
  {
    "date": "2021-12-22",
    "open": 50.09,
    "close": 51.84,
    "high": 52.84,
    "low": 49.87,
    "index": 255
  },
  {
    "date": "2021-12-23",
    "open": 51.28,
    "close": 51.49,
    "high": 51.81,
    "low": 50.49,
    "index": 256
  },
  {
    "date": "2021-12-24",
    "open": 50.9,
    "close": 52.1,
    "high": 52.77,
    "low": 50.89,
    "index": 257
  },
  {
    "date": "2021-12-27",
    "open": 51.67,
    "close": 53.34,
    "high": 55.25,
    "low": 50.84,
    "index": 258
  },
  {
    "date": "2021-12-28",
    "open": 53.4,
    "close": 54.06,
    "high": 54.49,
    "low": 52.63,
    "index": 259
  },
  {
    "date": "2021-12-29",
    "open": 54.42,
    "close": 54.25,
    "high": 54.64,
    "low": 52.72,
    "index": 260
  },
  {
    "date": "2021-12-30",
    "open": 53.62,
    "close": 55.95,
    "high": 56.92,
    "low": 53.62,
    "index": 261
  },
  {
    "date": "2021-12-31",
    "open": 55.71,
    "close": 56.09,
    "high": 56.91,
    "low": 54.77,
    "index": 262
  },
  {
    "date": "2022-01-04",
    "open": 55.09,
    "close": 51.77,
    "high": 55.49,
    "low": 51.67,
    "index": 263
  },
  {
    "date": "2022-01-05",
    "open": 51.67,
    "close": 50.83,
    "high": 53.34,
    "low": 49.77,
    "index": 264
  },
  {
    "date": "2022-01-06",
    "open": 50.51,
    "close": 50.84,
    "high": 51.33,
    "low": 49.71,
    "index": 265
  },
  {
    "date": "2022-01-07",
    "open": 50.82,
    "close": 49.58,
    "high": 51.17,
    "low": 49.49,
    "index": 266
  },
  {
    "date": "2022-01-10",
    "open": 49.59,
    "close": 50.4,
    "high": 51.07,
    "low": 49.09,
    "index": 267
  },
  {
    "date": "2022-01-11",
    "open": 49.86,
    "close": 47.17,
    "high": 50.39,
    "low": 47.13,
    "index": 268
  },
  {
    "date": "2022-01-12",
    "open": 47.34,
    "close": 47.43,
    "high": 47.92,
    "low": 46.39,
    "index": 269
  },
  {
    "date": "2022-01-13",
    "open": 47.27,
    "close": 47.41,
    "high": 48.94,
    "low": 46.77,
    "index": 270
  },
  {
    "date": "2022-01-14",
    "open": 47.63,
    "close": 47.94,
    "high": 48.34,
    "low": 46.39,
    "index": 271
  },
  {
    "date": "2022-01-17",
    "open": 47.72,
    "close": 50.56,
    "high": 51.77,
    "low": 47.48,
    "index": 272
  },
  {
    "date": "2022-01-18",
    "open": 50.56,
    "close": 49.36,
    "high": 51.09,
    "low": 48.79,
    "index": 273
  },
  {
    "date": "2022-01-19",
    "open": 49.34,
    "close": 48.42,
    "high": 50.09,
    "low": 48.27,
    "index": 274
  },
  {
    "date": "2022-01-20",
    "open": 48.89,
    "close": 48.09,
    "high": 49.35,
    "low": 47.7,
    "index": 275
  },
  {
    "date": "2022-01-21",
    "open": 48.12,
    "close": 48.27,
    "high": 49.77,
    "low": 47.27,
    "index": 276
  },
  {
    "date": "2022-01-24",
    "open": 47.78,
    "close": 46.7,
    "high": 48.26,
    "low": 46.57,
    "index": 277
  },
  {
    "date": "2022-01-25",
    "open": 46.7,
    "close": 45.09,
    "high": 47.18,
    "low": 44.41,
    "index": 278
  },
  {
    "date": "2022-01-26",
    "open": 45.77,
    "close": 43.64,
    "high": 45.93,
    "low": 43.34,
    "index": 279
  },
  {
    "date": "2022-01-27",
    "open": 43.82,
    "close": 42.19,
    "high": 45.2,
    "low": 41.92,
    "index": 280
  },
  {
    "date": "2022-01-28",
    "open": 42.65,
    "close": 42.19,
    "high": 43.28,
    "low": 41.93,
    "index": 281
  },
  {
    "date": "2022-02-07",
    "open": 43.1,
    "close": 41.99,
    "high": 43.22,
    "low": 41.12,
    "index": 282
  },
  {
    "date": "2022-02-08",
    "open": 41.92,
    "close": 41.01,
    "high": 41.92,
    "low": 40.5,
    "index": 283
  },
  {
    "date": "2022-02-09",
    "open": 41.02,
    "close": 42.34,
    "high": 42.47,
    "low": 40.49,
    "index": 284
  },
  {
    "date": "2022-02-10",
    "open": 42.35,
    "close": 40.85,
    "high": 42.35,
    "low": 39.77,
    "index": 285
  },
  {
    "date": "2022-02-11",
    "open": 40.47,
    "close": 40.35,
    "high": 41.12,
    "low": 40.22,
    "index": 286
  },
  {
    "date": "2022-02-14",
    "open": 40.35,
    "close": 40.22,
    "high": 40.73,
    "low": 39.77,
    "index": 287
  },
  {
    "date": "2022-02-15",
    "open": 39.96,
    "close": 40.19,
    "high": 40.62,
    "low": 39.81,
    "index": 288
  },
  {
    "date": "2022-02-16",
    "open": 40.56,
    "close": 40.79,
    "high": 41.28,
    "low": 39.92,
    "index": 289
  },
  {
    "date": "2022-02-17",
    "open": 40.91,
    "close": 40.97,
    "high": 41.53,
    "low": 40.35,
    "index": 290
  },
  {
    "date": "2022-02-18",
    "open": 40.57,
    "close": 40.64,
    "high": 41.27,
    "low": 40.43,
    "index": 291
  },
  {
    "date": "2022-02-21",
    "open": 41.14,
    "close": 41.77,
    "high": 41.77,
    "low": 40.27,
    "index": 292
  },
  {
    "date": "2022-02-22",
    "open": 41.69,
    "close": 40.48,
    "high": 41.69,
    "low": 40.04,
    "index": 293
  },
  {
    "date": "2022-02-23",
    "open": 40.59,
    "close": 40.92,
    "high": 41.92,
    "low": 40.42,
    "index": 294
  },
  {
    "date": "2022-02-24",
    "open": 40.84,
    "close": 41.13,
    "high": 42.59,
    "low": 40.12,
    "index": 295
  },
  {
    "date": "2022-02-25",
    "open": 41.02,
    "close": 41.35,
    "high": 41.88,
    "low": 41.02,
    "index": 296
  },
  {
    "date": "2022-02-28",
    "open": 41.72,
    "close": 40.62,
    "high": 41.92,
    "low": 40.35,
    "index": 297
  },
  {
    "date": "2022-03-01",
    "open": 40.84,
    "close": 39.02,
    "high": 40.84,
    "low": 38.42,
    "index": 298
  },
  {
    "date": "2022-03-02",
    "open": 38.67,
    "close": 38.45,
    "high": 38.83,
    "low": 38.26,
    "index": 299
  },
  {
    "date": "2022-03-03",
    "open": 38.45,
    "close": 37.49,
    "high": 39.02,
    "low": 37.29,
    "index": 300
  },
  {
    "date": "2022-03-04",
    "open": 37.49,
    "close": 37.04,
    "high": 38.19,
    "low": 36.84,
    "index": 301
  },
  {
    "date": "2022-03-07",
    "open": 36.92,
    "close": 33.7,
    "high": 37.26,
    "low": 33.29,
    "index": 302
  },
  {
    "date": "2022-03-08",
    "open": 33.67,
    "close": 31.7,
    "high": 33.67,
    "low": 31.62,
    "index": 303
  },
  {
    "date": "2022-03-09",
    "open": 31.87,
    "close": 30.91,
    "high": 32.03,
    "low": 29.5,
    "index": 304
  },
  {
    "date": "2022-03-10",
    "open": 31.49,
    "close": 30.44,
    "high": 32.08,
    "low": 30.29,
    "index": 305
  },
  {
    "date": "2022-03-11",
    "open": 30.25,
    "close": 30.49,
    "high": 30.86,
    "low": 29.42,
    "index": 306
  },
  {
    "date": "2022-03-14",
    "open": 30.34,
    "close": 29.58,
    "high": 30.57,
    "low": 29.29,
    "index": 307
  },
  {
    "date": "2022-03-15",
    "open": 29.24,
    "close": 27.93,
    "high": 29.72,
    "low": 27.82,
    "index": 308
  },
  {
    "date": "2022-03-16",
    "open": 28.35,
    "close": 28.49,
    "high": 28.91,
    "low": 27.2,
    "index": 309
  },
  {
    "date": "2022-03-17",
    "open": 29.12,
    "close": 30.04,
    "high": 30.34,
    "low": 28.85,
    "index": 310
  },
  {
    "date": "2022-03-18",
    "open": 29.87,
    "close": 29.56,
    "high": 30.04,
    "low": 29.13,
    "index": 311
  },
  {
    "date": "2022-03-21",
    "open": 29.59,
    "close": 29.27,
    "high": 30.46,
    "low": 28.4,
    "index": 312
  },
  {
    "date": "2022-03-22",
    "open": 29.21,
    "close": 28.59,
    "high": 29.38,
    "low": 28.37,
    "index": 313
  },
  {
    "date": "2022-03-23",
    "open": 28.69,
    "close": 29.27,
    "high": 30.12,
    "low": 28.65,
    "index": 314
  },
  {
    "date": "2022-03-24",
    "open": 29.06,
    "close": 28.84,
    "high": 29.14,
    "low": 28.09,
    "index": 315
  },
  {
    "date": "2022-03-25",
    "open": 29.04,
    "close": 28.17,
    "high": 29.22,
    "low": 28.0,
    "index": 316
  },
  {
    "date": "2022-03-28",
    "open": 28.04,
    "close": 27.63,
    "high": 28.04,
    "low": 27.01,
    "index": 317
  },
  {
    "date": "2022-03-29",
    "open": 27.7,
    "close": 26.82,
    "high": 28.22,
    "low": 26.78,
    "index": 318
  },
  {
    "date": "2022-03-30",
    "open": 27.33,
    "close": 27.78,
    "high": 27.81,
    "low": 26.86,
    "index": 319
  },
  {
    "date": "2022-03-31",
    "open": 27.77,
    "close": 27.24,
    "high": 27.77,
    "low": 27.2,
    "index": 320
  },
  {
    "date": "2022-04-01",
    "open": 27.24,
    "close": 26.97,
    "high": 27.36,
    "low": 26.62,
    "index": 321
  },
  {
    "date": "2022-04-06",
    "open": 26.75,
    "close": 26.65,
    "high": 26.79,
    "low": 25.92,
    "index": 322
  },
  {
    "date": "2022-04-07",
    "open": 26.58,
    "close": 25.76,
    "high": 26.77,
    "low": 25.76,
    "index": 323
  },
  {
    "date": "2022-04-08",
    "open": 25.75,
    "close": 25.28,
    "high": 25.87,
    "low": 24.81,
    "index": 324
  },
  {
    "date": "2022-04-11",
    "open": 25.04,
    "close": 23.99,
    "high": 25.27,
    "low": 23.9,
    "index": 325
  },
  {
    "date": "2022-04-12",
    "open": 23.76,
    "close": 24.3,
    "high": 24.34,
    "low": 23.39,
    "index": 326
  },
  {
    "date": "2022-04-13",
    "open": 24.3,
    "close": 23.4,
    "high": 24.3,
    "low": 23.37,
    "index": 327
  },
  {
    "date": "2022-04-14",
    "open": 23.4,
    "close": 23.34,
    "high": 24.01,
    "low": 23.15,
    "index": 328
  },
  {
    "date": "2022-04-15",
    "open": 23.24,
    "close": 22.84,
    "high": 23.24,
    "low": 22.5,
    "index": 329
  },
  {
    "date": "2022-04-18",
    "open": 22.92,
    "close": 23.22,
    "high": 23.46,
    "low": 22.29,
    "index": 330
  },
  {
    "date": "2022-04-19",
    "open": 23.2,
    "close": 22.42,
    "high": 23.7,
    "low": 21.92,
    "index": 331
  },
  {
    "date": "2022-04-20",
    "open": 22.71,
    "close": 21.99,
    "high": 22.84,
    "low": 21.95,
    "index": 332
  },
  {
    "date": "2022-04-21",
    "open": 21.89,
    "close": 21.16,
    "high": 22.17,
    "low": 21.09,
    "index": 333
  },
  {
    "date": "2022-04-22",
    "open": 20.69,
    "close": 20.41,
    "high": 21.04,
    "low": 20.13,
    "index": 334
  },
  {
    "date": "2022-04-25",
    "open": 20.13,
    "close": 18.56,
    "high": 20.14,
    "low": 18.37,
    "index": 335
  },
  {
    "date": "2022-04-26",
    "open": 18.62,
    "close": 17.39,
    "high": 18.72,
    "low": 17.39,
    "index": 336
  },
  {
    "date": "2022-04-27",
    "open": 16.84,
    "close": 18.24,
    "high": 18.54,
    "low": 16.77,
    "index": 337
  },
  {
    "date": "2022-04-28",
    "open": 18.24,
    "close": 18.01,
    "high": 18.38,
    "low": 17.62,
    "index": 338
  },
  {
    "date": "2022-04-29",
    "open": 18.02,
    "close": 19.06,
    "high": 19.12,
    "low": 17.42,
    "index": 339
  },
  {
    "date": "2022-05-05",
    "open": 19.07,
    "close": 19.77,
    "high": 19.89,
    "low": 18.7,
    "index": 340
  },
  {
    "date": "2022-05-06",
    "open": 19.56,
    "close": 19.34,
    "high": 19.56,
    "low": 19.04,
    "index": 341
  },
  {
    "date": "2022-05-09",
    "open": 19.39,
    "close": 19.92,
    "high": 20.2,
    "low": 19.27,
    "index": 342
  },
  {
    "date": "2022-05-10",
    "open": 19.67,
    "close": 21.2,
    "high": 21.69,
    "low": 19.35,
    "index": 343
  },
  {
    "date": "2022-05-11",
    "open": 21.28,
    "close": 21.34,
    "high": 21.97,
    "low": 20.78,
    "index": 344
  },
  {
    "date": "2022-05-12",
    "open": 20.99,
    "close": 21.53,
    "high": 21.63,
    "low": 20.89,
    "index": 345
  },
  {
    "date": "2022-05-13",
    "open": 21.73,
    "close": 21.77,
    "high": 22.47,
    "low": 21.6,
    "index": 346
  },
  {
    "date": "2022-05-16",
    "open": 21.76,
    "close": 21.25,
    "high": 22.24,
    "low": 21.2,
    "index": 347
  },
  {
    "date": "2022-05-17",
    "open": 21.26,
    "close": 21.27,
    "high": 21.55,
    "low": 20.92,
    "index": 348
  },
  {
    "date": "2022-05-18",
    "open": 21.27,
    "close": 21.69,
    "high": 22.39,
    "low": 21.2,
    "index": 349
  },
  {
    "date": "2022-05-19",
    "open": 21.27,
    "close": 21.54,
    "high": 21.71,
    "low": 21.12,
    "index": 350
  },
  {
    "date": "2022-05-20",
    "open": 21.9,
    "close": 22.29,
    "high": 22.54,
    "low": 21.54,
    "index": 351
  },
  {
    "date": "2022-05-23",
    "open": 22.34,
    "close": 22.58,
    "high": 22.92,
    "low": 22.13,
    "index": 352
  },
  {
    "date": "2022-05-24",
    "open": 22.63,
    "close": 21.51,
    "high": 23.32,
    "low": 21.46,
    "index": 353
  },
  {
    "date": "2022-05-25",
    "open": 21.37,
    "close": 21.47,
    "high": 21.51,
    "low": 20.92,
    "index": 354
  },
  {
    "date": "2022-05-26",
    "open": 21.52,
    "close": 21.27,
    "high": 21.63,
    "low": 20.7,
    "index": 355
  },
  {
    "date": "2022-05-27",
    "open": 21.49,
    "close": 21.83,
    "high": 22.39,
    "low": 21.38,
    "index": 356
  },
  {
    "date": "2022-05-30",
    "open": 21.77,
    "close": 21.94,
    "high": 22.27,
    "low": 21.13,
    "index": 357
  },
  {
    "date": "2022-05-31",
    "open": 21.96,
    "close": 24.22,
    "high": 24.22,
    "low": 21.64,
    "index": 358
  },
  {
    "date": "2022-06-01",
    "open": 25.38,
    "close": 25.67,
    "high": 26.72,
    "low": 24.57,
    "index": 359
  },
  {
    "date": "2022-06-02",
    "open": 25.67,
    "close": 28.32,
    "high": 28.32,
    "low": 25.16,
    "index": 360
  },
  {
    "date": "2022-06-06",
    "open": 28.24,
    "close": 29.27,
    "high": 30.39,
    "low": 25.4,
    "index": 361
  },
  {
    "date": "2022-06-07",
    "open": 28.12,
    "close": 27.22,
    "high": 28.79,
    "low": 27.07,
    "index": 362
  },
  {
    "date": "2022-06-08",
    "open": 27.38,
    "close": 27.34,
    "high": 27.45,
    "low": 26.18,
    "index": 363
  },
  {
    "date": "2022-06-09",
    "open": 27.27,
    "close": 26.17,
    "high": 27.27,
    "low": 25.84,
    "index": 364
  },
  {
    "date": "2022-06-10",
    "open": 26.13,
    "close": 27.79,
    "high": 28.59,
    "low": 25.72,
    "index": 365
  },
  {
    "date": "2022-06-13",
    "open": 27.79,
    "close": 27.52,
    "high": 28.61,
    "low": 27.27,
    "index": 366
  },
  {
    "date": "2022-06-14",
    "open": 27.39,
    "close": 28.33,
    "high": 28.51,
    "low": 26.02,
    "index": 367
  },
  {
    "date": "2022-06-15",
    "open": 28.44,
    "close": 31.24,
    "high": 31.24,
    "low": 28.39,
    "index": 368
  },
  {
    "date": "2022-06-16",
    "open": 31.25,
    "close": 31.59,
    "high": 34.45,
    "low": 31.25,
    "index": 369
  },
  {
    "date": "2022-06-17",
    "open": 31.59,
    "close": 33.69,
    "high": 34.84,
    "low": 30.37,
    "index": 370
  },
  {
    "date": "2022-06-20",
    "open": 33.1,
    "close": 34.42,
    "high": 36.73,
    "low": 32.42,
    "index": 371
  },
  {
    "date": "2022-06-21",
    "open": 35.27,
    "close": 33.63,
    "high": 35.27,
    "low": 32.49,
    "index": 372
  },
  {
    "date": "2022-06-22",
    "open": 33.47,
    "close": 32.32,
    "high": 33.54,
    "low": 32.14,
    "index": 373
  },
  {
    "date": "2022-06-23",
    "open": 32.42,
    "close": 33.89,
    "high": 34.44,
    "low": 31.76,
    "index": 374
  },
  {
    "date": "2022-06-24",
    "open": 33.43,
    "close": 33.03,
    "high": 33.89,
    "low": 32.65,
    "index": 375
  },
  {
    "date": "2022-06-27",
    "open": 33.02,
    "close": 35.24,
    "high": 36.41,
    "low": 32.26,
    "index": 376
  },
  {
    "date": "2022-06-28",
    "open": 36.49,
    "close": 38.84,
    "high": 38.84,
    "low": 34.42,
    "index": 377
  },
  {
    "date": "2022-06-29",
    "open": 38.84,
    "close": 35.34,
    "high": 40.33,
    "low": 35.33,
    "index": 378
  },
  {
    "date": "2022-06-30",
    "open": 34.92,
    "close": 33.32,
    "high": 35.34,
    "low": 32.44,
    "index": 379
  },
  {
    "date": "2022-07-01",
    "open": 33.3,
    "close": 33.1,
    "high": 34.62,
    "low": 32.38,
    "index": 380
  },
  {
    "date": "2022-07-04",
    "open": 33.09,
    "close": 31.57,
    "high": 33.1,
    "low": 30.63,
    "index": 381
  },
  {
    "date": "2022-07-05",
    "open": 30.92,
    "close": 31.23,
    "high": 32.99,
    "low": 30.69,
    "index": 382
  },
  {
    "date": "2022-07-06",
    "open": 31.04,
    "close": 30.97,
    "high": 31.77,
    "low": 30.22,
    "index": 383
  },
  {
    "date": "2022-07-07",
    "open": 30.97,
    "close": 31.93,
    "high": 32.84,
    "low": 30.49,
    "index": 384
  },
  {
    "date": "2022-07-08",
    "open": 32.17,
    "close": 35.2,
    "high": 35.2,
    "low": 32.17,
    "index": 385
  },
  {
    "date": "2022-07-11",
    "open": 35.19,
    "close": 35.63,
    "high": 36.1,
    "low": 32.88,
    "index": 386
  },
  {
    "date": "2022-07-12",
    "open": 35.06,
    "close": 31.99,
    "high": 35.06,
    "low": 31.99,
    "index": 387
  },
  {
    "date": "2022-07-13",
    "open": 31.85,
    "close": 31.34,
    "high": 32.42,
    "low": 30.7,
    "index": 388
  },
  {
    "date": "2022-07-14",
    "open": 31.35,
    "close": 33.07,
    "high": 33.2,
    "low": 31.16,
    "index": 389
  },
  {
    "date": "2022-07-15",
    "open": 32.87,
    "close": 34.12,
    "high": 35.27,
    "low": 32.79,
    "index": 390
  },
  {
    "date": "2022-07-18",
    "open": 34.12,
    "close": 34.49,
    "high": 35.49,
    "low": 33.73,
    "index": 391
  },
  {
    "date": "2022-07-19",
    "open": 34.67,
    "close": 34.29,
    "high": 35.38,
    "low": 33.49,
    "index": 392
  },
  {
    "date": "2022-07-20",
    "open": 34.65,
    "close": 33.4,
    "high": 34.65,
    "low": 33.07,
    "index": 393
  },
  {
    "date": "2022-07-21",
    "open": 33.17,
    "close": 33.17,
    "high": 34.4,
    "low": 32.99,
    "index": 394
  },
  {
    "date": "2022-07-22",
    "open": 33.49,
    "close": 32.84,
    "high": 33.7,
    "low": 32.32,
    "index": 395
  },
  {
    "date": "2022-07-25",
    "open": 32.86,
    "close": 31.69,
    "high": 33.2,
    "low": 31.56,
    "index": 396
  },
  {
    "date": "2022-07-26",
    "open": 32.07,
    "close": 32.2,
    "high": 32.31,
    "low": 31.35,
    "index": 397
  },
  {
    "date": "2022-07-27",
    "open": 32.02,
    "close": 35.5,
    "high": 35.5,
    "low": 31.78,
    "index": 398
  },
  {
    "date": "2022-07-28",
    "open": 35.99,
    "close": 39.13,
    "high": 39.13,
    "low": 35.87,
    "index": 399
  },
  {
    "date": "2022-07-29",
    "open": 37.85,
    "close": 38.82,
    "high": 39.84,
    "low": 37.42,
    "index": 400
  },
  {
    "date": "2022-08-01",
    "open": 39.62,
    "close": 38.2,
    "high": 40.26,
    "low": 37.38,
    "index": 401
  },
  {
    "date": "2022-08-02",
    "open": 37.97,
    "close": 36.38,
    "high": 38.2,
    "low": 35.98,
    "index": 402
  },
  {
    "date": "2022-08-03",
    "open": 36.75,
    "close": 35.55,
    "high": 37.49,
    "low": 35.34,
    "index": 403
  },
  {
    "date": "2022-08-04",
    "open": 35.16,
    "close": 33.82,
    "high": 35.72,
    "low": 33.51,
    "index": 404
  },
  {
    "date": "2022-08-05",
    "open": 33.91,
    "close": 37.28,
    "high": 37.28,
    "low": 33.41,
    "index": 405
  },
  {
    "date": "2022-08-08",
    "open": 38.16,
    "close": 41.09,
    "high": 41.09,
    "low": 36.26,
    "index": 406
  },
  {
    "date": "2022-08-09",
    "open": 41.2,
    "close": 42.7,
    "high": 45.2,
    "low": 40.49,
    "index": 407
  },
  {
    "date": "2022-08-10",
    "open": 42.42,
    "close": 44.96,
    "high": 46.82,
    "low": 41.82,
    "index": 408
  },
  {
    "date": "2022-08-11",
    "open": 44.34,
    "close": 44.2,
    "high": 45.56,
    "low": 43.04,
    "index": 409
  },
  {
    "date": "2022-08-12",
    "open": 45.54,
    "close": 44.37,
    "high": 46.62,
    "low": 43.62,
    "index": 410
  },
  {
    "date": "2022-08-15",
    "open": 43.49,
    "close": 43.82,
    "high": 45.12,
    "low": 42.71,
    "index": 411
  },
  {
    "date": "2022-08-16",
    "open": 43.82,
    "close": 43.09,
    "high": 44.12,
    "low": 42.78,
    "index": 412
  },
  {
    "date": "2022-08-17",
    "open": 43.75,
    "close": 47.49,
    "high": 47.49,
    "low": 43.27,
    "index": 413
  },
  {
    "date": "2022-08-18",
    "open": 47.49,
    "close": 49.91,
    "high": 50.77,
    "low": 47.09,
    "index": 414
  },
  {
    "date": "2022-08-19",
    "open": 48.84,
    "close": 46.76,
    "high": 49.91,
    "low": 46.56,
    "index": 415
  },
  {
    "date": "2022-08-22",
    "open": 46.83,
    "close": 48.74,
    "high": 50.98,
    "low": 46.83,
    "index": 416
  },
  {
    "date": "2022-08-23",
    "open": 48.77,
    "close": 46.34,
    "high": 50.04,
    "low": 45.92,
    "index": 417
  },
  {
    "date": "2022-08-24",
    "open": 46.34,
    "close": 42.73,
    "high": 46.42,
    "low": 42.39,
    "index": 418
  },
  {
    "date": "2022-08-25",
    "open": 42.77,
    "close": 44.57,
    "high": 45.92,
    "low": 42.77,
    "index": 419
  },
  {
    "date": "2022-08-26",
    "open": 44.12,
    "close": 41.23,
    "high": 44.6,
    "low": 41.02,
    "index": 420
  },
  {
    "date": "2022-08-29",
    "open": 40.72,
    "close": 45.43,
    "high": 45.43,
    "low": 39.82,
    "index": 421
  },
  {
    "date": "2022-08-30",
    "open": 47.34,
    "close": 46.06,
    "high": 47.79,
    "low": 45.19,
    "index": 422
  },
  {
    "date": "2022-08-31",
    "open": 46.34,
    "close": 41.38,
    "high": 46.82,
    "low": 41.37,
    "index": 423
  },
  {
    "date": "2022-09-01",
    "open": 40.99,
    "close": 39.7,
    "high": 41.34,
    "low": 38.37,
    "index": 424
  },
  {
    "date": "2022-09-02",
    "open": 40.22,
    "close": 42.84,
    "high": 43.75,
    "low": 38.64,
    "index": 425
  },
  {
    "date": "2022-09-05",
    "open": 43.13,
    "close": 41.06,
    "high": 43.31,
    "low": 40.64,
    "index": 426
  },
  {
    "date": "2022-09-06",
    "open": 41.27,
    "close": 43.17,
    "high": 43.49,
    "low": 40.27,
    "index": 427
  },
  {
    "date": "2022-09-07",
    "open": 42.77,
    "close": 47.57,
    "high": 47.57,
    "low": 41.77,
    "index": 428
  },
  {
    "date": "2022-09-08",
    "open": 48.53,
    "close": 44.14,
    "high": 50.77,
    "low": 43.84,
    "index": 429
  },
  {
    "date": "2022-09-09",
    "open": 44.61,
    "close": 42.58,
    "high": 44.74,
    "low": 41.08,
    "index": 430
  },
  {
    "date": "2022-09-13",
    "open": 42.93,
    "close": 46.92,
    "high": 46.92,
    "low": 42.67,
    "index": 431
  },
  {
    "date": "2022-09-14",
    "open": 45.96,
    "close": 45.99,
    "high": 47.83,
    "low": 45.16,
    "index": 432
  },
  {
    "date": "2022-09-15",
    "open": 46.17,
    "close": 44.22,
    "high": 46.49,
    "low": 42.78,
    "index": 433
  },
  {
    "date": "2022-09-16",
    "open": 43.84,
    "close": 48.72,
    "high": 48.72,
    "low": 43.32,
    "index": 434
  },
  {
    "date": "2022-09-19",
    "open": 48.78,
    "close": 48.69,
    "high": 49.9,
    "low": 46.2,
    "index": 435
  },
  {
    "date": "2022-09-20",
    "open": 48.24,
    "close": 49.53,
    "high": 50.55,
    "low": 47.07,
    "index": 436
  },
  {
    "date": "2022-09-21",
    "open": 49.03,
    "close": 50.57,
    "high": 52.31,
    "low": 48.5,
    "index": 437
  },
  {
    "date": "2022-09-22",
    "open": 49.83,
    "close": 48.17,
    "high": 50.74,
    "low": 47.84,
    "index": 438
  },
  {
    "date": "2022-09-23",
    "open": 49.63,
    "close": 44.59,
    "high": 49.63,
    "low": 43.27,
    "index": 439
  },
  {
    "date": "2022-09-26",
    "open": 43.92,
    "close": 44.67,
    "high": 46.08,
    "low": 43.49,
    "index": 440
  },
  {
    "date": "2022-09-27",
    "open": 44.46,
    "close": 44.82,
    "high": 46.63,
    "low": 43.32,
    "index": 441
  },
  {
    "date": "2022-09-28",
    "open": 45.2,
    "close": 40.25,
    "high": 45.2,
    "low": 40.25,
    "index": 442
  },
  {
    "date": "2022-09-29",
    "open": 39.91,
    "close": 39.2,
    "high": 40.25,
    "low": 37.77,
    "index": 443
  },
  {
    "date": "2022-09-30",
    "open": 38.63,
    "close": 35.85,
    "high": 39.2,
    "low": 35.81,
    "index": 444
  },
  {
    "date": "2022-10-10",
    "open": 35.81,
    "close": 34.31,
    "high": 36.45,
    "low": 34.04,
    "index": 445
  },
  {
    "date": "2022-10-11",
    "open": 33.95,
    "close": 34.06,
    "high": 35.56,
    "low": 33.49,
    "index": 446
  },
  {
    "date": "2022-10-12",
    "open": 33.56,
    "close": 37.02,
    "high": 37.49,
    "low": 33.42,
    "index": 447
  },
  {
    "date": "2022-10-13",
    "open": 36.34,
    "close": 35.8,
    "high": 37.7,
    "low": 35.52,
    "index": 448
  },
  {
    "date": "2022-10-14",
    "open": 36.92,
    "close": 36.42,
    "high": 37.06,
    "low": 35.44,
    "index": 449
  },
  {
    "date": "2022-10-17",
    "open": 36.34,
    "close": 37.12,
    "high": 37.31,
    "low": 35.26,
    "index": 450
  },
  {
    "date": "2022-10-18",
    "open": 37.56,
    "close": 38.46,
    "high": 38.49,
    "low": 36.36,
    "index": 451
  },
  {
    "date": "2022-10-19",
    "open": 38.23,
    "close": 39.03,
    "high": 40.55,
    "low": 37.42,
    "index": 452
  },
  {
    "date": "2022-10-20",
    "open": 38.47,
    "close": 37.85,
    "high": 38.47,
    "low": 36.76,
    "index": 453
  },
  {
    "date": "2022-10-21",
    "open": 37.44,
    "close": 36.76,
    "high": 38.13,
    "low": 36.52,
    "index": 454
  },
  {
    "date": "2022-10-24",
    "open": 36.39,
    "close": 35.94,
    "high": 37.45,
    "low": 35.49,
    "index": 455
  },
  {
    "date": "2022-10-25",
    "open": 35.93,
    "close": 36.4,
    "high": 36.94,
    "low": 35.04,
    "index": 456
  },
  {
    "date": "2022-10-26",
    "open": 36.41,
    "close": 36.93,
    "high": 38.08,
    "low": 35.78,
    "index": 457
  },
  {
    "date": "2022-10-27",
    "open": 37.47,
    "close": 37.89,
    "high": 40.33,
    "low": 37.47,
    "index": 458
  },
  {
    "date": "2022-10-28",
    "open": 37.89,
    "close": 35.92,
    "high": 38.05,
    "low": 35.83,
    "index": 459
  },
  {
    "date": "2022-10-31",
    "open": 35.99,
    "close": 36.7,
    "high": 36.89,
    "low": 35.49,
    "index": 460
  },
  {
    "date": "2022-11-01",
    "open": 36.72,
    "close": 38.57,
    "high": 39.58,
    "low": 36.26,
    "index": 461
  },
  {
    "date": "2022-11-02",
    "open": 42.51,
    "close": 42.51,
    "high": 42.51,
    "low": 41.63,
    "index": 462
  },
  {
    "date": "2022-11-03",
    "open": 42.42,
    "close": 42.49,
    "high": 44.76,
    "low": 41.71,
    "index": 463
  },
  {
    "date": "2022-11-04",
    "open": 42.69,
    "close": 43.42,
    "high": 43.96,
    "low": 41.44,
    "index": 464
  },
  {
    "date": "2022-11-07",
    "open": 43.27,
    "close": 44.79,
    "high": 46.13,
    "low": 42.47,
    "index": 465
  },
  {
    "date": "2022-11-08",
    "open": 44.19,
    "close": 44.01,
    "high": 44.69,
    "low": 43.49,
    "index": 466
  },
  {
    "date": "2022-11-09",
    "open": 43.92,
    "close": 43.5,
    "high": 44.15,
    "low": 42.06,
    "index": 467
  },
  {
    "date": "2022-11-10",
    "open": 43.16,
    "close": 42.78,
    "high": 43.72,
    "low": 41.79,
    "index": 468
  },
  {
    "date": "2022-11-11",
    "open": 43.67,
    "close": 41.77,
    "high": 44.4,
    "low": 40.63,
    "index": 469
  },
  {
    "date": "2022-11-14",
    "open": 40.99,
    "close": 41.2,
    "high": 42.49,
    "low": 40.9,
    "index": 470
  },
  {
    "date": "2022-11-15",
    "open": 40.99,
    "close": 42.03,
    "high": 42.16,
    "low": 40.49,
    "index": 471
  },
  {
    "date": "2022-11-16",
    "open": 42.02,
    "close": 42.22,
    "high": 42.55,
    "low": 41.27,
    "index": 472
  },
  {
    "date": "2022-11-17",
    "open": 42.49,
    "close": 41.23,
    "high": 42.54,
    "low": 40.63,
    "index": 473
  },
  {
    "date": "2022-11-18",
    "open": 41.28,
    "close": 43.53,
    "high": 44.19,
    "low": 40.87,
    "index": 474
  },
  {
    "date": "2022-11-21",
    "open": 43.13,
    "close": 43.54,
    "high": 44.83,
    "low": 42.57,
    "index": 475
  },
  {
    "date": "2022-11-22",
    "open": 42.99,
    "close": 40.8,
    "high": 43.63,
    "low": 40.65,
    "index": 476
  },
  {
    "date": "2022-11-23",
    "open": 40.77,
    "close": 40.31,
    "high": 41.01,
    "low": 39.64,
    "index": 477
  },
  {
    "date": "2022-11-24",
    "open": 40.3,
    "close": 42.14,
    "high": 42.47,
    "low": 40.27,
    "index": 478
  },
  {
    "date": "2022-11-25",
    "open": 41.72,
    "close": 40.06,
    "high": 41.99,
    "low": 39.89,
    "index": 479
  },
  {
    "date": "2022-11-28",
    "open": 38.54,
    "close": 39.83,
    "high": 39.84,
    "low": 38.48,
    "index": 480
  },
  {
    "date": "2022-11-29",
    "open": 40.63,
    "close": 39.99,
    "high": 40.63,
    "low": 39.27,
    "index": 481
  },
  {
    "date": "2022-11-30",
    "open": 39.99,
    "close": 39.36,
    "high": 40.09,
    "low": 38.9,
    "index": 482
  },
  {
    "date": "2022-12-01",
    "open": 40.27,
    "close": 40.16,
    "high": 40.63,
    "low": 39.22,
    "index": 483
  },
  {
    "date": "2022-12-02",
    "open": 39.92,
    "close": 40.34,
    "high": 40.99,
    "low": 39.61,
    "index": 484
  },
  {
    "date": "2022-12-05",
    "open": 40.35,
    "close": 38.66,
    "high": 40.62,
    "low": 38.13,
    "index": 485
  },
  {
    "date": "2022-12-06",
    "open": 38.2,
    "close": 38.95,
    "high": 39.06,
    "low": 38.2,
    "index": 486
  },
  {
    "date": "2022-12-07",
    "open": 38.57,
    "close": 38.67,
    "high": 39.52,
    "low": 38.24,
    "index": 487
  },
  {
    "date": "2022-12-08",
    "open": 38.49,
    "close": 38.51,
    "high": 39.11,
    "low": 38.24,
    "index": 488
  },
  {
    "date": "2022-12-09",
    "open": 38.34,
    "close": 38.21,
    "high": 39.06,
    "low": 38.04,
    "index": 489
  },
  {
    "date": "2022-12-12",
    "open": 37.78,
    "close": 37.91,
    "high": 38.7,
    "low": 37.29,
    "index": 490
  },
  {
    "date": "2022-12-13",
    "open": 37.75,
    "close": 36.7,
    "high": 38.13,
    "low": 36.38,
    "index": 491
  },
  {
    "date": "2022-12-14",
    "open": 37.38,
    "close": 35.81,
    "high": 37.38,
    "low": 35.42,
    "index": 492
  },
  {
    "date": "2022-12-15",
    "open": 35.85,
    "close": 36.45,
    "high": 36.7,
    "low": 35.54,
    "index": 493
  },
  {
    "date": "2022-12-16",
    "open": 36.15,
    "close": 34.99,
    "high": 36.55,
    "low": 34.59,
    "index": 494
  },
  {
    "date": "2022-12-19",
    "open": 34.91,
    "close": 34.31,
    "high": 35.25,
    "low": 34.16,
    "index": 495
  },
  {
    "date": "2022-12-20",
    "open": 34.13,
    "close": 34.97,
    "high": 35.9,
    "low": 34.13,
    "index": 496
  },
  {
    "date": "2022-12-21",
    "open": 34.59,
    "close": 34.35,
    "high": 35.17,
    "low": 34.12,
    "index": 497
  },
  {
    "date": "2022-12-22",
    "open": 34.2,
    "close": 33.24,
    "high": 34.53,
    "low": 33.17,
    "index": 498
  },
  {
    "date": "2022-12-23",
    "open": 32.92,
    "close": 32.77,
    "high": 33.34,
    "low": 31.85,
    "index": 499
  },
  {
    "date": "2022-12-26",
    "open": 32.53,
    "close": 34.53,
    "high": 34.69,
    "low": 32.39,
    "index": 500
  },
  {
    "date": "2022-12-27",
    "open": 35.06,
    "close": 34.13,
    "high": 35.06,
    "low": 33.82,
    "index": 501
  },
  {
    "date": "2022-12-28",
    "open": 33.96,
    "close": 33.2,
    "high": 34.2,
    "low": 32.94,
    "index": 502
  },
  {
    "date": "2022-12-29",
    "open": 33.62,
    "close": 33.92,
    "high": 34.19,
    "low": 32.78,
    "index": 503
  },
  {
    "date": "2022-12-30",
    "open": 33.98,
    "close": 33.54,
    "high": 34.48,
    "low": 33.2,
    "index": 504
  },
  {
    "date": "2023-01-03",
    "open": 33.54,
    "close": 35.15,
    "high": 35.27,
    "low": 33.54,
    "index": 505
  },
  {
    "date": "2023-01-04",
    "open": 35.15,
    "close": 34.47,
    "high": 35.56,
    "low": 34.14,
    "index": 506
  },
  {
    "date": "2023-01-05",
    "open": 34.47,
    "close": 36.06,
    "high": 36.6,
    "low": 34.12,
    "index": 507
  },
  {
    "date": "2023-01-06",
    "open": 36.06,
    "close": 35.71,
    "high": 36.88,
    "low": 35.35,
    "index": 508
  },
  {
    "date": "2023-01-09",
    "open": 35.68,
    "close": 36.95,
    "high": 37.66,
    "low": 35.67,
    "index": 509
  },
  {
    "date": "2023-01-10",
    "open": 36.99,
    "close": 37.06,
    "high": 37.66,
    "low": 36.2,
    "index": 510
  },
  {
    "date": "2023-01-11",
    "open": 37.34,
    "close": 37.01,
    "high": 38.34,
    "low": 36.63,
    "index": 511
  },
  {
    "date": "2023-01-12",
    "open": 37.04,
    "close": 37.5,
    "high": 38.26,
    "low": 36.77,
    "index": 512
  },
  {
    "date": "2023-01-13",
    "open": 37.54,
    "close": 36.92,
    "high": 37.92,
    "low": 36.82,
    "index": 513
  },
  {
    "date": "2023-01-16",
    "open": 37.44,
    "close": 36.44,
    "high": 37.55,
    "low": 36.27,
    "index": 514
  },
  {
    "date": "2023-01-17",
    "open": 36.57,
    "close": 38.83,
    "high": 39.2,
    "low": 36.12,
    "index": 515
  },
  {
    "date": "2023-01-18",
    "open": 38.5,
    "close": 37.89,
    "high": 38.74,
    "low": 37.64,
    "index": 516
  },
  {
    "date": "2023-01-19",
    "open": 37.65,
    "close": 37.85,
    "high": 38.27,
    "low": 37.24,
    "index": 517
  },
  {
    "date": "2023-01-20",
    "open": 38.48,
    "close": 40.05,
    "high": 40.89,
    "low": 38.02,
    "index": 518
  },
  {
    "date": "2023-01-30",
    "open": 40.47,
    "close": 39.42,
    "high": 41.55,
    "low": 38.76,
    "index": 519
  },
  {
    "date": "2023-01-31",
    "open": 39.2,
    "close": 40.57,
    "high": 41.12,
    "low": 39.13,
    "index": 520
  },
  {
    "date": "2023-02-01",
    "open": 40.62,
    "close": 41.31,
    "high": 41.77,
    "low": 40.18,
    "index": 521
  },
  {
    "date": "2023-02-02",
    "open": 41.85,
    "close": 44.15,
    "high": 44.92,
    "low": 41.82,
    "index": 522
  },
  {
    "date": "2023-02-03",
    "open": 43.96,
    "close": 45.35,
    "high": 45.78,
    "low": 43.26,
    "index": 523
  },
  {
    "date": "2023-02-06",
    "open": 44.77,
    "close": 44.89,
    "high": 46.04,
    "low": 43.99,
    "index": 524
  },
  {
    "date": "2023-02-07",
    "open": 45.13,
    "close": 49.46,
    "high": 49.46,
    "low": 44.89,
    "index": 525
  },
  {
    "date": "2023-02-08",
    "open": 49.61,
    "close": 50.47,
    "high": 50.99,
    "low": 47.99,
    "index": 526
  },
  {
    "date": "2023-02-09",
    "open": 49.7,
    "close": 48.98,
    "high": 49.95,
    "low": 47.13,
    "index": 527
  },
  {
    "date": "2023-02-10",
    "open": 48.41,
    "close": 49.32,
    "high": 51.48,
    "low": 48.29,
    "index": 528
  },
  {
    "date": "2023-02-13",
    "open": 48.82,
    "close": 49.04,
    "high": 50.77,
    "low": 48.02,
    "index": 529
  },
  {
    "date": "2023-02-14",
    "open": 49.08,
    "close": 48.49,
    "high": 49.2,
    "low": 48.06,
    "index": 530
  },
  {
    "date": "2023-02-15",
    "open": 48.37,
    "close": 50.34,
    "high": 52.75,
    "low": 48.14,
    "index": 531
  },
  {
    "date": "2023-02-16",
    "open": 51.37,
    "close": 47.89,
    "high": 51.44,
    "low": 47.32,
    "index": 532
  },
  {
    "date": "2023-02-17",
    "open": 47.77,
    "close": 47.63,
    "high": 49.35,
    "low": 47.06,
    "index": 533
  },
  {
    "date": "2023-02-20",
    "open": 47.55,
    "close": 47.42,
    "high": 47.87,
    "low": 46.7,
    "index": 534
  },
  {
    "date": "2023-02-21",
    "open": 47.04,
    "close": 45.59,
    "high": 48.08,
    "low": 45.39,
    "index": 535
  },
  {
    "date": "2023-02-22",
    "open": 45.27,
    "close": 46.31,
    "high": 47.31,
    "low": 44.99,
    "index": 536
  },
  {
    "date": "2023-02-23",
    "open": 46.32,
    "close": 47.48,
    "high": 47.63,
    "low": 45.76,
    "index": 537
  },
  {
    "date": "2023-02-24",
    "open": 47.06,
    "close": 46.82,
    "high": 47.48,
    "low": 45.89,
    "index": 538
  },
  {
    "date": "2023-02-27",
    "open": 46.81,
    "close": 46.15,
    "high": 47.05,
    "low": 45.65,
    "index": 539
  },
  {
    "date": "2023-02-28",
    "open": 46.05,
    "close": 45.99,
    "high": 46.34,
    "low": 45.22,
    "index": 540
  },
  {
    "date": "2023-03-01",
    "open": 45.97,
    "close": 45.99,
    "high": 46.18,
    "low": 44.84,
    "index": 541
  },
  {
    "date": "2023-03-02",
    "open": 45.97,
    "close": 45.34,
    "high": 46.84,
    "low": 45.27,
    "index": 542
  },
  {
    "date": "2023-03-03",
    "open": 45.35,
    "close": 44.38,
    "high": 45.89,
    "low": 44.2,
    "index": 543
  },
  {
    "date": "2023-03-06",
    "open": 44.54,
    "close": 45.11,
    "high": 45.49,
    "low": 43.7,
    "index": 544
  },
  {
    "date": "2023-03-07",
    "open": 46.41,
    "close": 43.88,
    "high": 46.57,
    "low": 43.83,
    "index": 545
  },
  {
    "date": "2023-03-08",
    "open": 44.29,
    "close": 43.96,
    "high": 44.77,
    "low": 43.49,
    "index": 546
  },
  {
    "date": "2023-03-09",
    "open": 43.97,
    "close": 44.2,
    "high": 45.27,
    "low": 43.96,
    "index": 547
  },
  {
    "date": "2023-03-10",
    "open": 43.84,
    "close": 43.77,
    "high": 44.37,
    "low": 43.31,
    "index": 548
  },
  {
    "date": "2023-03-13",
    "open": 43.77,
    "close": 44.93,
    "high": 45.0,
    "low": 43.14,
    "index": 549
  },
  {
    "date": "2023-03-14",
    "open": 44.67,
    "close": 43.96,
    "high": 44.91,
    "low": 43.07,
    "index": 550
  },
  {
    "date": "2023-03-15",
    "open": 44.28,
    "close": 45.4,
    "high": 48.13,
    "low": 43.9,
    "index": 551
  },
  {
    "date": "2023-03-16",
    "open": 45.85,
    "close": 44.14,
    "high": 46.04,
    "low": 44.04,
    "index": 552
  },
  {
    "date": "2023-03-17",
    "open": 44.92,
    "close": 46.19,
    "high": 46.59,
    "low": 44.58,
    "index": 553
  },
  {
    "date": "2023-03-20",
    "open": 47.06,
    "close": 47.69,
    "high": 49.19,
    "low": 46.17,
    "index": 554
  },
  {
    "date": "2023-03-21",
    "open": 47.69,
    "close": 49.49,
    "high": 50.77,
    "low": 46.72,
    "index": 555
  },
  {
    "date": "2023-03-22",
    "open": 49.56,
    "close": 48.96,
    "high": 50.27,
    "low": 48.5,
    "index": 556
  },
  {
    "date": "2023-03-23",
    "open": 48.96,
    "close": 49.5,
    "high": 49.92,
    "low": 48.48,
    "index": 557
  },
  {
    "date": "2023-03-24",
    "open": 51.92,
    "close": 54.53,
    "high": 54.53,
    "low": 50.99,
    "index": 558
  },
  {
    "date": "2023-03-27",
    "open": 55.24,
    "close": 56.5,
    "high": 59.63,
    "low": 54.27,
    "index": 559
  },
  {
    "date": "2023-03-28",
    "open": 56.32,
    "close": 55.97,
    "high": 58.44,
    "low": 54.69,
    "index": 560
  },
  {
    "date": "2023-03-29",
    "open": 55.41,
    "close": 56.19,
    "high": 58.04,
    "low": 55.34,
    "index": 561
  },
  {
    "date": "2023-03-30",
    "open": 57.89,
    "close": 61.89,
    "high": 61.89,
    "low": 57.87,
    "index": 562
  },
  {
    "date": "2023-03-31",
    "open": 60.36,
    "close": 55.62,
    "high": 61.86,
    "low": 55.62,
    "index": 563
  },
  {
    "date": "2023-04-03",
    "open": 54.42,
    "close": 57.68,
    "high": 58.3,
    "low": 54.34,
    "index": 564
  },
  {
    "date": "2023-04-04",
    "open": 57.77,
    "close": 55.92,
    "high": 59.2,
    "low": 55.24,
    "index": 565
  },
  {
    "date": "2023-04-06",
    "open": 54.97,
    "close": 58.54,
    "high": 58.54,
    "low": 54.83,
    "index": 566
  },
  {
    "date": "2023-04-07",
    "open": 58.03,
    "close": 56.42,
    "high": 59.34,
    "low": 55.63,
    "index": 567
  },
  {
    "date": "2023-04-10",
    "open": 57.92,
    "close": 56.27,
    "high": 61.77,
    "low": 55.79,
    "index": 568
  },
  {
    "date": "2023-04-11",
    "open": 56.28,
    "close": 55.15,
    "high": 57.02,
    "low": 54.94,
    "index": 569
  },
  {
    "date": "2023-04-12",
    "open": 58.13,
    "close": 60.74,
    "high": 60.74,
    "low": 57.12,
    "index": 570
  },
  {
    "date": "2023-04-13",
    "open": 61.15,
    "close": 55.82,
    "high": 61.7,
    "low": 55.4,
    "index": 571
  },
  {
    "date": "2023-04-14",
    "open": 55.97,
    "close": 55.56,
    "high": 56.47,
    "low": 54.63,
    "index": 572
  },
  {
    "date": "2023-04-17",
    "open": 54.2,
    "close": 52.88,
    "high": 54.32,
    "low": 51.34,
    "index": 573
  },
  {
    "date": "2023-04-18",
    "open": 52.06,
    "close": 52.59,
    "high": 53.18,
    "low": 51.99,
    "index": 574
  },
  {
    "date": "2023-04-19",
    "open": 51.64,
    "close": 54.55,
    "high": 56.53,
    "low": 51.13,
    "index": 575
  },
  {
    "date": "2023-04-20",
    "open": 54.2,
    "close": 54.56,
    "high": 55.62,
    "low": 53.62,
    "index": 576
  },
  {
    "date": "2023-04-21",
    "open": 53.63,
    "close": 51.94,
    "high": 55.25,
    "low": 51.82,
    "index": 577
  },
  {
    "date": "2023-04-24",
    "open": 52.92,
    "close": 54.56,
    "high": 56.06,
    "low": 52.92,
    "index": 578
  },
  {
    "date": "2023-04-25",
    "open": 54.56,
    "close": 51.74,
    "high": 54.84,
    "low": 50.63,
    "index": 579
  },
  {
    "date": "2023-04-26",
    "open": 50.86,
    "close": 50.63,
    "high": 53.68,
    "low": 50.22,
    "index": 580
  },
  {
    "date": "2023-04-27",
    "open": 51.1,
    "close": 49.36,
    "high": 53.13,
    "low": 48.77,
    "index": 581
  },
  {
    "date": "2023-04-28",
    "open": 49.74,
    "close": 48.56,
    "high": 50.39,
    "low": 48.27,
    "index": 582
  },
  {
    "date": "2023-05-04",
    "open": 49.02,
    "close": 49.99,
    "high": 52.54,
    "low": 48.57,
    "index": 583
  },
  {
    "date": "2023-05-05",
    "open": 50.01,
    "close": 50.5,
    "high": 52.74,
    "low": 49.13,
    "index": 584
  },
  {
    "date": "2023-05-08",
    "open": 50.82,
    "close": 50.92,
    "high": 52.49,
    "low": 49.22,
    "index": 585
  },
  {
    "date": "2023-05-09",
    "open": 50.63,
    "close": 56.09,
    "high": 56.09,
    "low": 49.56,
    "index": 586
  },
  {
    "date": "2023-05-10",
    "open": 57.06,
    "close": 59.92,
    "high": 61.77,
    "low": 57.06,
    "index": 587
  },
  {
    "date": "2023-05-11",
    "open": 61.1,
    "close": 61.69,
    "high": 63.32,
    "low": 59.04,
    "index": 588
  },
  {
    "date": "2023-05-12",
    "open": 61.68,
    "close": 60.08,
    "high": 63.32,
    "low": 59.33,
    "index": 589
  },
  {
    "date": "2023-05-15",
    "open": 61.89,
    "close": 58.49,
    "high": 64.27,
    "low": 56.69,
    "index": 590
  },
  {
    "date": "2023-05-16",
    "open": 59.47,
    "close": 62.75,
    "high": 63.65,
    "low": 58.47,
    "index": 591
  },
  {
    "date": "2023-05-17",
    "open": 61.47,
    "close": 61.75,
    "high": 65.04,
    "low": 59.22,
    "index": 592
  },
  {
    "date": "2023-05-18",
    "open": 62.18,
    "close": 66.68,
    "high": 67.91,
    "low": 61.47,
    "index": 593
  },
  {
    "date": "2023-05-19",
    "open": 67.34,
    "close": 66.74,
    "high": 72.31,
    "low": 64.66,
    "index": 594
  },
  {
    "date": "2023-05-22",
    "open": 65.73,
    "close": 62.46,
    "high": 66.65,
    "low": 61.8,
    "index": 595
  },
  {
    "date": "2023-05-23",
    "open": 61.47,
    "close": 61.05,
    "high": 63.81,
    "low": 59.93,
    "index": 596
  },
  {
    "date": "2023-05-24",
    "open": 60.64,
    "close": 60.0,
    "high": 61.47,
    "low": 57.89,
    "index": 597
  },
  {
    "date": "2023-05-25",
    "open": 59.97,
    "close": 60.25,
    "high": 61.82,
    "low": 58.61,
    "index": 598
  },
  {
    "date": "2023-05-26",
    "open": 60.75,
    "close": 63.18,
    "high": 63.61,
    "low": 59.09,
    "index": 599
  },
  {
    "date": "2023-05-29",
    "open": 63.18,
    "close": 62.32,
    "high": 65.0,
    "low": 60.31,
    "index": 600
  },
  {
    "date": "2023-05-30",
    "open": 61.32,
    "close": 62.75,
    "high": 64.32,
    "low": 60.39,
    "index": 601
  },
  {
    "date": "2023-05-31",
    "open": 63.61,
    "close": 66.47,
    "high": 67.88,
    "low": 62.89,
    "index": 602
  },
  {
    "date": "2023-06-01",
    "open": 67.32,
    "close": 67.18,
    "high": 69.55,
    "low": 64.57,
    "index": 603
  },
  {
    "date": "2023-06-02",
    "open": 69.32,
    "close": 60.39,
    "high": 70.04,
    "low": 60.39,
    "index": 604
  },
  {
    "date": "2023-06-05",
    "open": 59.32,
    "close": 60.82,
    "high": 61.66,
    "low": 58.39,
    "index": 605
  },
  {
    "date": "2023-06-06",
    "open": 58.25,
    "close": 54.67,
    "high": 58.36,
    "low": 54.67,
    "index": 606
  },
  {
    "date": "2023-06-07",
    "open": 52.3,
    "close": 53.47,
    "high": 56.61,
    "low": 50.04,
    "index": 607
  },
  {
    "date": "2023-06-08",
    "open": 53.64,
    "close": 52.18,
    "high": 55.66,
    "low": 51.97,
    "index": 608
  },
  {
    "date": "2023-06-09",
    "open": 51.59,
    "close": 50.59,
    "high": 52.49,
    "low": 49.29,
    "index": 609
  },
  {
    "date": "2023-06-12",
    "open": 50.42,
    "close": 53.76,
    "high": 54.4,
    "low": 50.04,
    "index": 610
  },
  {
    "date": "2023-06-13",
    "open": 53.67,
    "close": 52.63,
    "high": 53.67,
    "low": 52.24,
    "index": 611
  },
  {
    "date": "2023-06-14",
    "open": 52.54,
    "close": 51.68,
    "high": 52.89,
    "low": 51.47,
    "index": 612
  },
  {
    "date": "2023-06-15",
    "open": 51.32,
    "close": 52.39,
    "high": 53.67,
    "low": 50.96,
    "index": 613
  },
  {
    "date": "2023-06-16",
    "open": 53.61,
    "close": 55.65,
    "high": 55.82,
    "low": 51.13,
    "index": 614
  },
  {
    "date": "2023-06-19",
    "open": 54.32,
    "close": 56.33,
    "high": 56.47,
    "low": 53.98,
    "index": 615
  },
  {
    "date": "2023-06-20",
    "open": 55.75,
    "close": 60.64,
    "high": 61.87,
    "low": 55.27,
    "index": 616
  },
  {
    "date": "2023-06-21",
    "open": 61.47,
    "close": 57.95,
    "high": 61.82,
    "low": 57.89,
    "index": 617
  },
  {
    "date": "2023-06-26",
    "open": 57.86,
    "close": 53.64,
    "high": 58.22,
    "low": 53.17,
    "index": 618
  },
  {
    "date": "2023-06-27",
    "open": 53.53,
    "close": 56.86,
    "high": 57.61,
    "low": 52.22,
    "index": 619
  },
  {
    "date": "2023-06-28",
    "open": 56.86,
    "close": 56.37,
    "high": 58.04,
    "low": 54.04,
    "index": 620
  },
  {
    "date": "2023-06-29",
    "open": 56.77,
    "close": 60.68,
    "high": 61.31,
    "low": 55.97,
    "index": 621
  },
  {
    "date": "2023-06-30",
    "open": 60.82,
    "close": 61.99,
    "high": 63.04,
    "low": 60.04,
    "index": 622
  },
  {
    "date": "2023-07-03",
    "open": 63.84,
    "close": 61.09,
    "high": 65.96,
    "low": 60.04,
    "index": 623
  },
  {
    "date": "2023-07-04",
    "open": 61.52,
    "close": 66.32,
    "high": 66.32,
    "low": 60.75,
    "index": 624
  },
  {
    "date": "2023-07-05",
    "open": 64.74,
    "close": 61.37,
    "high": 64.87,
    "low": 61.11,
    "index": 625
  },
  {
    "date": "2023-07-06",
    "open": 61.37,
    "close": 61.27,
    "high": 62.44,
    "low": 60.57,
    "index": 626
  },
  {
    "date": "2023-07-07",
    "open": 61.18,
    "close": 58.61,
    "high": 61.23,
    "low": 57.75,
    "index": 627
  },
  {
    "date": "2023-07-10",
    "open": 58.45,
    "close": 56.01,
    "high": 58.94,
    "low": 55.88,
    "index": 628
  },
  {
    "date": "2023-07-11",
    "open": 56.01,
    "close": 56.58,
    "high": 56.97,
    "low": 54.47,
    "index": 629
  },
  {
    "date": "2023-07-12",
    "open": 56.58,
    "close": 54.22,
    "high": 56.75,
    "low": 54.19,
    "index": 630
  },
  {
    "date": "2023-07-13",
    "open": 54.25,
    "close": 55.39,
    "high": 56.47,
    "low": 54.1,
    "index": 631
  },
  {
    "date": "2023-07-14",
    "open": 55.3,
    "close": 56.79,
    "high": 57.53,
    "low": 54.43,
    "index": 632
  },
  {
    "date": "2023-07-17",
    "open": 56.46,
    "close": 56.04,
    "high": 56.68,
    "low": 55.23,
    "index": 633
  },
  {
    "date": "2023-07-18",
    "open": 55.76,
    "close": 58.25,
    "high": 59.5,
    "low": 55.48,
    "index": 634
  },
  {
    "date": "2023-07-19",
    "open": 57.41,
    "close": 58.23,
    "high": 58.64,
    "low": 56.59,
    "index": 635
  },
  {
    "date": "2023-07-20",
    "open": 57.9,
    "close": 52.58,
    "high": 58.27,
    "low": 52.58,
    "index": 636
  },
  {
    "date": "2023-07-21",
    "open": 52.18,
    "close": 53.17,
    "high": 53.24,
    "low": 50.69,
    "index": 637
  },
  {
    "date": "2023-07-24",
    "open": 52.87,
    "close": 53.14,
    "high": 53.97,
    "low": 51.84,
    "index": 638
  },
  {
    "date": "2023-07-25",
    "open": 53.37,
    "close": 54.11,
    "high": 54.77,
    "low": 53.18,
    "index": 639
  },
  {
    "date": "2023-07-26",
    "open": 53.79,
    "close": 52.64,
    "high": 54.33,
    "low": 52.18,
    "index": 640
  },
  {
    "date": "2023-07-27",
    "open": 52.64,
    "close": 51.06,
    "high": 53.22,
    "low": 50.75,
    "index": 641
  },
  {
    "date": "2023-07-28",
    "open": 51.47,
    "close": 49.94,
    "high": 51.47,
    "low": 49.43,
    "index": 642
  },
  {
    "date": "2023-07-31",
    "open": 49.87,
    "close": 50.31,
    "high": 50.72,
    "low": 49.75,
    "index": 643
  },
  {
    "date": "2023-08-01",
    "open": 50.04,
    "close": 51.42,
    "high": 51.78,
    "low": 48.32,
    "index": 644
  },
  {
    "date": "2023-08-02",
    "open": 50.82,
    "close": 50.97,
    "high": 51.69,
    "low": 50.39,
    "index": 645
  },
  {
    "date": "2023-08-03",
    "open": 50.7,
    "close": 52.18,
    "high": 52.78,
    "low": 50.27,
    "index": 646
  },
  {
    "date": "2023-08-04",
    "open": 52.18,
    "close": 52.22,
    "high": 53.02,
    "low": 51.84,
    "index": 647
  },
  {
    "date": "2023-08-07",
    "open": 51.82,
    "close": 52.52,
    "high": 54.08,
    "low": 51.78,
    "index": 648
  },
  {
    "date": "2023-08-08",
    "open": 52.67,
    "close": 51.52,
    "high": 53.32,
    "low": 50.89,
    "index": 649
  },
  {
    "date": "2023-08-09",
    "open": 51.53,
    "close": 50.15,
    "high": 51.8,
    "low": 49.97,
    "index": 650
  },
  {
    "date": "2023-08-10",
    "open": 50.15,
    "close": 48.72,
    "high": 50.72,
    "low": 48.62,
    "index": 651
  },
  {
    "date": "2023-08-11",
    "open": 49.02,
    "close": 47.58,
    "high": 49.04,
    "low": 46.94,
    "index": 652
  },
  {
    "date": "2023-08-14",
    "open": 46.67,
    "close": 47.44,
    "high": 47.67,
    "low": 46.0,
    "index": 653
  },
  {
    "date": "2023-08-15",
    "open": 47.54,
    "close": 46.52,
    "high": 47.74,
    "low": 45.79,
    "index": 654
  },
  {
    "date": "2023-08-16",
    "open": 46.5,
    "close": 44.47,
    "high": 46.67,
    "low": 44.32,
    "index": 655
  },
  {
    "date": "2023-08-17",
    "open": 44.69,
    "close": 45.82,
    "high": 46.13,
    "low": 43.9,
    "index": 656
  },
  {
    "date": "2023-08-18",
    "open": 45.67,
    "close": 44.68,
    "high": 46.19,
    "low": 44.68,
    "index": 657
  },
  {
    "date": "2023-08-21",
    "open": 44.25,
    "close": 44.17,
    "high": 45.4,
    "low": 44.11,
    "index": 658
  },
  {
    "date": "2023-08-22",
    "open": 44.49,
    "close": 48.66,
    "high": 48.66,
    "low": 44.49,
    "index": 659
  },
  {
    "date": "2023-08-23",
    "open": 48.11,
    "close": 48.02,
    "high": 48.89,
    "low": 47.13,
    "index": 660
  },
  {
    "date": "2023-08-24",
    "open": 48.45,
    "close": 49.07,
    "high": 50.75,
    "low": 48.04,
    "index": 661
  },
  {
    "date": "2023-08-25",
    "open": 48.35,
    "close": 47.89,
    "high": 48.52,
    "low": 46.99,
    "index": 662
  },
  {
    "date": "2023-08-28",
    "open": 50.39,
    "close": 48.33,
    "high": 50.39,
    "low": 47.11,
    "index": 663
  },
  {
    "date": "2023-08-29",
    "open": 47.69,
    "close": 51.89,
    "high": 52.04,
    "low": 47.25,
    "index": 664
  },
  {
    "date": "2023-08-30",
    "open": 51.47,
    "close": 52.72,
    "high": 53.52,
    "low": 50.99,
    "index": 665
  },
  {
    "date": "2023-08-31",
    "open": 52.86,
    "close": 52.06,
    "high": 53.61,
    "low": 51.87,
    "index": 666
  },
  {
    "date": "2023-09-01",
    "open": 51.82,
    "close": 50.53,
    "high": 52.02,
    "low": 49.87,
    "index": 667
  },
  {
    "date": "2023-09-04",
    "open": 50.9,
    "close": 50.89,
    "high": 51.59,
    "low": 49.61,
    "index": 668
  },
  {
    "date": "2023-09-05",
    "open": 50.91,
    "close": 51.0,
    "high": 51.89,
    "low": 50.11,
    "index": 669
  },
  {
    "date": "2023-09-06",
    "open": 50.04,
    "close": 51.04,
    "high": 51.41,
    "low": 48.97,
    "index": 670
  },
  {
    "date": "2023-09-07",
    "open": 50.99,
    "close": 48.99,
    "high": 50.99,
    "low": 48.76,
    "index": 671
  },
  {
    "date": "2023-09-08",
    "open": 48.78,
    "close": 49.58,
    "high": 50.23,
    "low": 48.14,
    "index": 672
  },
  {
    "date": "2023-09-11",
    "open": 50.75,
    "close": 52.25,
    "high": 52.72,
    "low": 49.63,
    "index": 673
  },
  {
    "date": "2023-09-12",
    "open": 53.19,
    "close": 53.57,
    "high": 54.54,
    "low": 52.39,
    "index": 674
  },
  {
    "date": "2023-09-13",
    "open": 51.94,
    "close": 51.11,
    "high": 52.59,
    "low": 49.74,
    "index": 675
  },
  {
    "date": "2023-09-14",
    "open": 50.49,
    "close": 49.34,
    "high": 51.11,
    "low": 49.27,
    "index": 676
  },
  {
    "date": "2023-09-15",
    "open": 49.47,
    "close": 48.47,
    "high": 50.02,
    "low": 47.89,
    "index": 677
  },
  {
    "date": "2023-09-18",
    "open": 48.25,
    "close": 49.42,
    "high": 50.18,
    "low": 48.02,
    "index": 678
  },
  {
    "date": "2023-09-19",
    "open": 49.36,
    "close": 48.31,
    "high": 49.36,
    "low": 47.82,
    "index": 679
  },
  {
    "date": "2023-09-20",
    "open": 48.52,
    "close": 48.18,
    "high": 49.22,
    "low": 48.11,
    "index": 680
  },
  {
    "date": "2023-09-21",
    "open": 47.95,
    "close": 47.42,
    "high": 48.44,
    "low": 47.12,
    "index": 681
  },
  {
    "date": "2023-09-22",
    "open": 47.24,
    "close": 49.92,
    "high": 50.25,
    "low": 47.16,
    "index": 682
  },
  {
    "date": "2023-09-25",
    "open": 49.49,
    "close": 49.68,
    "high": 50.62,
    "low": 49.39,
    "index": 683
  },
  {
    "date": "2023-09-26",
    "open": 49.47,
    "close": 49.62,
    "high": 50.6,
    "low": 49.19,
    "index": 684
  },
  {
    "date": "2023-09-27",
    "open": 49.27,
    "close": 50.56,
    "high": 51.56,
    "low": 49.27,
    "index": 685
  },
  {
    "date": "2023-09-28",
    "open": 51.12,
    "close": 51.77,
    "high": 53.04,
    "low": 51.04,
    "index": 686
  },
  {
    "date": "2023-10-09",
    "open": 51.42,
    "close": 51.1,
    "high": 51.93,
    "low": 50.77,
    "index": 687
  },
  {
    "date": "2023-10-10",
    "open": 51.07,
    "close": 52.06,
    "high": 52.37,
    "low": 50.68,
    "index": 688
  },
  {
    "date": "2023-10-11",
    "open": 52.52,
    "close": 53.29,
    "high": 55.61,
    "low": 51.29,
    "index": 689
  },
  {
    "date": "2023-10-12",
    "open": 53.61,
    "close": 53.44,
    "high": 54.52,
    "low": 52.32,
    "index": 690
  },
  {
    "date": "2023-10-13",
    "open": 53.19,
    "close": 53.13,
    "high": 53.96,
    "low": 52.54,
    "index": 691
  },
  {
    "date": "2023-10-16",
    "open": 53.13,
    "close": 51.32,
    "high": 53.13,
    "low": 50.97,
    "index": 692
  },
  {
    "date": "2023-10-17",
    "open": 51.32,
    "close": 49.34,
    "high": 51.67,
    "low": 49.32,
    "index": 693
  },
  {
    "date": "2023-10-18",
    "open": 48.63,
    "close": 49.89,
    "high": 50.75,
    "low": 48.38,
    "index": 694
  },
  {
    "date": "2023-10-19",
    "open": 49.9,
    "close": 48.69,
    "high": 50.45,
    "low": 48.68,
    "index": 695
  },
  {
    "date": "2023-10-20",
    "open": 48.69,
    "close": 47.69,
    "high": 49.06,
    "low": 47.55,
    "index": 696
  },
  {
    "date": "2023-10-23",
    "open": 48.18,
    "close": 46.37,
    "high": 48.45,
    "low": 45.97,
    "index": 697
  },
  {
    "date": "2023-10-24",
    "open": 47.11,
    "close": 46.54,
    "high": 47.53,
    "low": 45.95,
    "index": 698
  },
  {
    "date": "2023-10-25",
    "open": 46.94,
    "close": 47.31,
    "high": 48.13,
    "low": 46.6,
    "index": 699
  },
  {
    "date": "2023-10-26",
    "open": 46.89,
    "close": 47.87,
    "high": 48.68,
    "low": 46.32,
    "index": 700
  },
  {
    "date": "2023-10-27",
    "open": 47.84,
    "close": 47.02,
    "high": 47.84,
    "low": 44.53,
    "index": 701
  },
  {
    "date": "2023-10-30",
    "open": 46.59,
    "close": 51.79,
    "high": 51.79,
    "low": 45.97,
    "index": 702
  },
  {
    "date": "2023-10-31",
    "open": 51.8,
    "close": 51.45,
    "high": 52.94,
    "low": 51.19,
    "index": 703
  },
  {
    "date": "2023-11-01",
    "open": 51.19,
    "close": 50.82,
    "high": 51.44,
    "low": 50.42,
    "index": 704
  },
  {
    "date": "2023-11-02",
    "open": 50.94,
    "close": 50.19,
    "high": 51.67,
    "low": 50.19,
    "index": 705
  },
  {
    "date": "2023-11-03",
    "open": 51.11,
    "close": 52.26,
    "high": 52.82,
    "low": 50.29,
    "index": 706
  },
  {
    "date": "2023-11-06",
    "open": 52.26,
    "close": 55.16,
    "high": 56.52,
    "low": 52.04,
    "index": 707
  },
  {
    "date": "2023-11-07",
    "open": 54.64,
    "close": 56.44,
    "high": 58.04,
    "low": 53.74,
    "index": 708
  },
  {
    "date": "2023-11-08",
    "open": 56.64,
    "close": 56.25,
    "high": 57.54,
    "low": 55.37,
    "index": 709
  },
  {
    "date": "2023-11-09",
    "open": 55.82,
    "close": 54.25,
    "high": 56.04,
    "low": 54.04,
    "index": 710
  },
  {
    "date": "2023-11-10",
    "open": 53.92,
    "close": 54.6,
    "high": 57.87,
    "low": 52.47,
    "index": 711
  },
  {
    "date": "2023-11-13",
    "open": 54.6,
    "close": 55.54,
    "high": 57.46,
    "low": 54.32,
    "index": 712
  },
  {
    "date": "2023-11-14",
    "open": 56.11,
    "close": 55.04,
    "high": 56.27,
    "low": 54.74,
    "index": 713
  },
  {
    "date": "2023-11-15",
    "open": 55.45,
    "close": 56.42,
    "high": 57.87,
    "low": 54.75,
    "index": 714
  },
  {
    "date": "2023-11-16",
    "open": 56.08,
    "close": 54.68,
    "high": 56.36,
    "low": 54.42,
    "index": 715
  },
  {
    "date": "2023-11-17",
    "open": 54.5,
    "close": 55.44,
    "high": 55.58,
    "low": 54.19,
    "index": 716
  },
  {
    "date": "2023-11-20",
    "open": 55.91,
    "close": 57.37,
    "high": 57.39,
    "low": 55.11,
    "index": 717
  },
  {
    "date": "2023-11-21",
    "open": 57.17,
    "close": 57.69,
    "high": 58.58,
    "low": 56.18,
    "index": 718
  },
  {
    "date": "2023-11-22",
    "open": 57.49,
    "close": 53.61,
    "high": 57.49,
    "low": 53.47,
    "index": 719
  },
  {
    "date": "2023-11-23",
    "open": 54.01,
    "close": 54.47,
    "high": 55.0,
    "low": 52.77,
    "index": 720
  },
  {
    "date": "2023-11-24",
    "open": 54.32,
    "close": 53.11,
    "high": 54.55,
    "low": 52.82,
    "index": 721
  },
  {
    "date": "2023-11-27",
    "open": 52.36,
    "close": 52.99,
    "high": 53.61,
    "low": 52.24,
    "index": 722
  },
  {
    "date": "2023-11-28",
    "open": 52.68,
    "close": 55.27,
    "high": 57.05,
    "low": 52.68,
    "index": 723
  },
  {
    "date": "2023-11-29",
    "open": 54.59,
    "close": 56.97,
    "high": 57.87,
    "low": 54.32,
    "index": 724
  },
  {
    "date": "2023-11-30",
    "open": 56.47,
    "close": 56.08,
    "high": 56.8,
    "low": 55.24,
    "index": 725
  },
  {
    "date": "2023-12-01",
    "open": 55.94,
    "close": 56.02,
    "high": 56.67,
    "low": 55.18,
    "index": 726
  },
  {
    "date": "2023-12-04",
    "open": 55.99,
    "close": 56.39,
    "high": 56.52,
    "low": 54.62,
    "index": 727
  },
  {
    "date": "2023-12-05",
    "open": 56.11,
    "close": 54.16,
    "high": 56.81,
    "low": 54.15,
    "index": 728
  },
  {
    "date": "2023-12-06",
    "open": 53.61,
    "close": 54.61,
    "high": 56.11,
    "low": 53.33,
    "index": 729
  },
  {
    "date": "2023-12-07",
    "open": 54.26,
    "close": 56.11,
    "high": 57.82,
    "low": 53.99,
    "index": 730
  },
  {
    "date": "2023-12-08",
    "open": 56.41,
    "close": 57.99,
    "high": 59.68,
    "low": 56.05,
    "index": 731
  },
  {
    "date": "2023-12-11",
    "open": 57.25,
    "close": 58.72,
    "high": 60.24,
    "low": 56.43,
    "index": 732
  },
  {
    "date": "2023-12-12",
    "open": 59.08,
    "close": 58.75,
    "high": 59.32,
    "low": 57.2,
    "index": 733
  },
  {
    "date": "2023-12-13",
    "open": 58.75,
    "close": 58.82,
    "high": 60.17,
    "low": 58.12,
    "index": 734
  },
  {
    "date": "2023-12-14",
    "open": 58.82,
    "close": 57.62,
    "high": 59.45,
    "low": 57.54,
    "index": 735
  },
  {
    "date": "2023-12-15",
    "open": 57.84,
    "close": 57.43,
    "high": 58.47,
    "low": 56.37,
    "index": 736
  },
  {
    "date": "2023-12-18",
    "open": 57.04,
    "close": 56.37,
    "high": 57.67,
    "low": 56.11,
    "index": 737
  },
  {
    "date": "2023-12-19",
    "open": 56.47,
    "close": 58.17,
    "high": 58.37,
    "low": 55.54,
    "index": 738
  },
  {
    "date": "2023-12-20",
    "open": 58.6,
    "close": 58.22,
    "high": 60.73,
    "low": 57.89,
    "index": 739
  },
  {
    "date": "2023-12-21",
    "open": 57.88,
    "close": 58.25,
    "high": 58.89,
    "low": 55.75,
    "index": 740
  },
  {
    "date": "2023-12-22",
    "open": 58.58,
    "close": 58.43,
    "high": 60.36,
    "low": 57.9,
    "index": 741
  },
  {
    "date": "2023-12-25",
    "open": 58.61,
    "close": 62.89,
    "high": 64.34,
    "low": 58.61,
    "index": 742
  },
  {
    "date": "2023-12-26",
    "open": 62.18,
    "close": 62.32,
    "high": 63.62,
    "low": 61.15,
    "index": 743
  },
  {
    "date": "2023-12-27",
    "open": 62.31,
    "close": 64.82,
    "high": 66.25,
    "low": 61.97,
    "index": 744
  },
  {
    "date": "2023-12-28",
    "open": 64.63,
    "close": 63.13,
    "high": 64.64,
    "low": 61.52,
    "index": 745
  },
  {
    "date": "2023-12-29",
    "open": 63.18,
    "close": 66.46,
    "high": 69.31,
    "low": 62.39,
    "index": 746
  },
  {
    "date": "2024-01-02",
    "open": 66.59,
    "close": 64.77,
    "high": 67.57,
    "low": 63.91,
    "index": 747
  },
  {
    "date": "2024-01-03",
    "open": 64.76,
    "close": 58.22,
    "high": 65.03,
    "low": 58.22,
    "index": 748
  },
  {
    "date": "2024-01-04",
    "open": 56.25,
    "close": 58.27,
    "high": 59.68,
    "low": 55.86,
    "index": 749
  },
  {
    "date": "2024-01-05",
    "open": 57.93,
    "close": 57.09,
    "high": 59.99,
    "low": 56.61,
    "index": 750
  },
  {
    "date": "2024-01-08",
    "open": 57.09,
    "close": 55.04,
    "high": 58.43,
    "low": 55.03,
    "index": 751
  },
  {
    "date": "2024-01-09",
    "open": 55.17,
    "close": 56.28,
    "high": 58.29,
    "low": 55.1,
    "index": 752
  },
  {
    "date": "2024-01-10",
    "open": 55.62,
    "close": 54.39,
    "high": 56.19,
    "low": 53.34,
    "index": 753
  },
  {
    "date": "2024-01-11",
    "open": 54.24,
    "close": 56.47,
    "high": 57.36,
    "low": 54.24,
    "index": 754
  },
  {
    "date": "2024-01-12",
    "open": 56.11,
    "close": 55.77,
    "high": 56.81,
    "low": 55.43,
    "index": 755
  },
  {
    "date": "2024-01-15",
    "open": 55.52,
    "close": 56.75,
    "high": 58.04,
    "low": 54.94,
    "index": 756
  },
  {
    "date": "2024-01-16",
    "open": 56.75,
    "close": 55.59,
    "high": 56.88,
    "low": 54.44,
    "index": 757
  },
  {
    "date": "2024-01-17",
    "open": 55.59,
    "close": 50.73,
    "high": 55.59,
    "low": 50.27,
    "index": 758
  },
  {
    "date": "2024-01-18",
    "open": 50.74,
    "close": 52.54,
    "high": 52.58,
    "low": 48.0,
    "index": 759
  },
  {
    "date": "2024-01-19",
    "open": 52.57,
    "close": 50.67,
    "high": 52.57,
    "low": 50.6,
    "index": 760
  },
  {
    "date": "2024-01-22",
    "open": 51.6,
    "close": 47.88,
    "high": 52.24,
    "low": 47.39,
    "index": 761
  },
  {
    "date": "2024-01-23",
    "open": 47.18,
    "close": 47.09,
    "high": 48.39,
    "low": 46.69,
    "index": 762
  },
  {
    "date": "2024-01-24",
    "open": 47.54,
    "close": 45.56,
    "high": 47.54,
    "low": 43.96,
    "index": 763
  },
  {
    "date": "2024-01-25",
    "open": 45.86,
    "close": 46.18,
    "high": 46.94,
    "low": 44.18,
    "index": 764
  },
  {
    "date": "2024-01-26",
    "open": 45.75,
    "close": 43.11,
    "high": 46.17,
    "low": 42.96,
    "index": 765
  },
  {
    "date": "2024-01-29",
    "open": 43.52,
    "close": 40.84,
    "high": 43.69,
    "low": 40.53,
    "index": 766
  },
  {
    "date": "2024-01-30",
    "open": 40.29,
    "close": 40.39,
    "high": 42.7,
    "low": 40.23,
    "index": 767
  },
  {
    "date": "2024-01-31",
    "open": 40.39,
    "close": 37.34,
    "high": 40.64,
    "low": 37.18,
    "index": 768
  },
  {
    "date": "2024-02-01",
    "open": 36.96,
    "close": 37.72,
    "high": 38.64,
    "low": 36.52,
    "index": 769
  },
  {
    "date": "2024-02-02",
    "open": 37.89,
    "close": 36.84,
    "high": 39.74,
    "low": 35.44,
    "index": 770
  },
  {
    "date": "2024-02-05",
    "open": 36.47,
    "close": 34.56,
    "high": 37.69,
    "low": 33.25,
    "index": 771
  },
  {
    "date": "2024-02-06",
    "open": 34.31,
    "close": 37.07,
    "high": 38.03,
    "low": 32.82,
    "index": 772
  },
  {
    "date": "2024-02-07",
    "open": 37.08,
    "close": 38.44,
    "high": 40.14,
    "low": 36.54,
    "index": 773
  },
  {
    "date": "2024-02-08",
    "open": 38.44,
    "close": 42.29,
    "high": 42.35,
    "low": 38.22,
    "index": 774
  },
  {
    "date": "2024-02-19",
    "open": 41.56,
    "close": 40.7,
    "high": 41.56,
    "low": 38.02,
    "index": 775
  },
  {
    "date": "2024-02-20",
    "open": 39.39,
    "close": 39.03,
    "high": 39.7,
    "low": 38.14,
    "index": 776
  },
  {
    "date": "2024-02-21",
    "open": 38.61,
    "close": 40.74,
    "high": 41.81,
    "low": 38.28,
    "index": 777
  },
  {
    "date": "2024-02-22",
    "open": 40.24,
    "close": 40.33,
    "high": 41.11,
    "low": 38.87,
    "index": 778
  },
  {
    "date": "2024-02-23",
    "open": 40.24,
    "close": 44.43,
    "high": 44.43,
    "low": 40.17,
    "index": 779
  },
  {
    "date": "2024-02-26",
    "open": 45.1,
    "close": 47.1,
    "high": 48.94,
    "low": 43.69,
    "index": 780
  },
  {
    "date": "2024-02-27",
    "open": 46.14,
    "close": 47.54,
    "high": 47.55,
    "low": 44.68,
    "index": 781
  },
  {
    "date": "2024-02-28",
    "open": 47.18,
    "close": 42.96,
    "high": 47.61,
    "low": 42.89,
    "index": 782
  },
  {
    "date": "2024-02-29",
    "open": 41.83,
    "close": 44.42,
    "high": 44.54,
    "low": 41.63,
    "index": 783
  },
  {
    "date": "2024-03-01",
    "open": 44.42,
    "close": 45.03,
    "high": 45.32,
    "low": 44.08,
    "index": 784
  },
  {
    "date": "2024-03-04",
    "open": 45.03,
    "close": 47.77,
    "high": 48.04,
    "low": 42.89,
    "index": 785
  },
  {
    "date": "2024-03-05",
    "open": 47.08,
    "close": 46.27,
    "high": 47.69,
    "low": 45.37,
    "index": 786
  },
  {
    "date": "2024-03-06",
    "open": 45.47,
    "close": 50.97,
    "high": 50.97,
    "low": 44.52,
    "index": 787
  },
  {
    "date": "2024-03-07",
    "open": 48.97,
    "close": 49.24,
    "high": 50.67,
    "low": 47.9,
    "index": 788
  },
  {
    "date": "2024-03-08",
    "open": 48.33,
    "close": 48.49,
    "high": 48.9,
    "low": 47.18,
    "index": 789
  },
  {
    "date": "2024-03-11",
    "open": 48.04,
    "close": 49.99,
    "high": 51.18,
    "low": 47.81,
    "index": 790
  },
  {
    "date": "2024-03-12",
    "open": 50.97,
    "close": 50.08,
    "high": 51.39,
    "low": 48.99,
    "index": 791
  },
  {
    "date": "2024-03-13",
    "open": 50.04,
    "close": 49.12,
    "high": 50.04,
    "low": 48.61,
    "index": 792
  },
  {
    "date": "2024-03-14",
    "open": 49.12,
    "close": 48.23,
    "high": 49.93,
    "low": 47.89,
    "index": 793
  },
  {
    "date": "2024-03-15",
    "open": 47.89,
    "close": 49.77,
    "high": 49.95,
    "low": 47.32,
    "index": 794
  },
  {
    "date": "2024-03-18",
    "open": 49.8,
    "close": 50.81,
    "high": 50.97,
    "low": 49.37,
    "index": 795
  },
  {
    "date": "2024-03-19",
    "open": 50.64,
    "close": 49.82,
    "high": 52.49,
    "low": 49.74,
    "index": 796
  },
  {
    "date": "2024-03-20",
    "open": 49.82,
    "close": 49.39,
    "high": 49.9,
    "low": 48.38,
    "index": 797
  },
  {
    "date": "2024-03-21",
    "open": 49.39,
    "close": 48.65,
    "high": 49.59,
    "low": 48.37,
    "index": 798
  },
  {
    "date": "2024-03-22",
    "open": 48.32,
    "close": 47.7,
    "high": 48.91,
    "low": 47.18,
    "index": 799
  },
  {
    "date": "2024-03-25",
    "open": 47.01,
    "close": 45.27,
    "high": 48.01,
    "low": 45.18,
    "index": 800
  },
  {
    "date": "2024-03-26",
    "open": 45.18,
    "close": 43.97,
    "high": 45.59,
    "low": 43.54,
    "index": 801
  },
  {
    "date": "2024-03-27",
    "open": 43.95,
    "close": 41.47,
    "high": 44.1,
    "low": 41.47,
    "index": 802
  },
  {
    "date": "2024-03-28",
    "open": 41.32,
    "close": 42.65,
    "high": 43.52,
    "low": 41.32,
    "index": 803
  },
  {
    "date": "2024-03-29",
    "open": 42.89,
    "close": 43.43,
    "high": 43.7,
    "low": 42.22,
    "index": 804
  },
  {
    "date": "2024-04-01",
    "open": 44.66,
    "close": 43.88,
    "high": 44.79,
    "low": 43.46,
    "index": 805
  },
  {
    "date": "2024-04-02",
    "open": 43.68,
    "close": 42.96,
    "high": 43.88,
    "low": 42.61,
    "index": 806
  },
  {
    "date": "2024-04-03",
    "open": 43.09,
    "close": 43.42,
    "high": 43.76,
    "low": 41.97,
    "index": 807
  },
  {
    "date": "2024-04-08",
    "open": 43.43,
    "close": 42.43,
    "high": 44.02,
    "low": 42.41,
    "index": 808
  },
  {
    "date": "2024-04-09",
    "open": 42.18,
    "close": 42.39,
    "high": 42.94,
    "low": 41.77,
    "index": 809
  },
  {
    "date": "2024-04-10",
    "open": 42.19,
    "close": 40.34,
    "high": 42.32,
    "low": 40.08,
    "index": 810
  },
  {
    "date": "2024-04-11",
    "open": 39.79,
    "close": 39.67,
    "high": 40.89,
    "low": 39.64,
    "index": 811
  },
  {
    "date": "2024-04-12",
    "open": 39.62,
    "close": 39.34,
    "high": 40.32,
    "low": 39.17,
    "index": 812
  },
  {
    "date": "2024-04-15",
    "open": 39.35,
    "close": 38.52,
    "high": 39.72,
    "low": 37.77,
    "index": 813
  },
  {
    "date": "2024-04-16",
    "open": 38.52,
    "close": 36.35,
    "high": 38.57,
    "low": 35.61,
    "index": 814
  },
  {
    "date": "2024-04-17",
    "open": 36.82,
    "close": 37.92,
    "high": 38.18,
    "low": 36.82,
    "index": 815
  },
  {
    "date": "2024-04-18",
    "open": 37.92,
    "close": 41.77,
    "high": 41.77,
    "low": 37.2,
    "index": 816
  },
  {
    "date": "2024-04-19",
    "open": 43.45,
    "close": 42.29,
    "high": 44.31,
    "low": 41.27,
    "index": 817
  },
  {
    "date": "2024-04-22",
    "open": 40.83,
    "close": 39.62,
    "high": 41.61,
    "low": 39.36,
    "index": 818
  },
  {
    "date": "2024-04-23",
    "open": 39.29,
    "close": 40.29,
    "high": 41.1,
    "low": 39.18,
    "index": 819
  },
  {
    "date": "2024-04-24",
    "open": 40.27,
    "close": 40.14,
    "high": 40.27,
    "low": 39.47,
    "index": 820
  },
  {
    "date": "2024-04-25",
    "open": 39.64,
    "close": 39.67,
    "high": 40.12,
    "low": 39.48,
    "index": 821
  },
  {
    "date": "2024-04-26",
    "open": 41.68,
    "close": 43.7,
    "high": 43.7,
    "low": 41.51,
    "index": 822
  },
  {
    "date": "2024-04-29",
    "open": 45.67,
    "close": 44.72,
    "high": 45.67,
    "low": 43.71,
    "index": 823
  },
  {
    "date": "2024-04-30",
    "open": 44.68,
    "close": 43.59,
    "high": 45.02,
    "low": 43.48,
    "index": 824
  },
  {
    "date": "2024-05-06",
    "open": 44.41,
    "close": 45.55,
    "high": 45.74,
    "low": 44.25,
    "index": 825
  },
  {
    "date": "2024-05-07",
    "open": 45.44,
    "close": 44.87,
    "high": 45.47,
    "low": 44.68,
    "index": 826
  },
  {
    "date": "2024-05-08",
    "open": 44.77,
    "close": 45.18,
    "high": 45.87,
    "low": 44.14,
    "index": 827
  },
  {
    "date": "2024-05-09",
    "open": 46.51,
    "close": 47.68,
    "high": 49.14,
    "low": 46.51,
    "index": 828
  },
  {
    "date": "2024-05-10",
    "open": 47.16,
    "close": 47.39,
    "high": 48.75,
    "low": 45.79,
    "index": 829
  },
  {
    "date": "2024-05-13",
    "open": 46.11,
    "close": 45.25,
    "high": 46.66,
    "low": 44.95,
    "index": 830
  },
  {
    "date": "2024-05-14",
    "open": 46.91,
    "close": 46.47,
    "high": 48.11,
    "low": 46.47,
    "index": 831
  },
  {
    "date": "2024-05-15",
    "open": 46.19,
    "close": 45.04,
    "high": 46.64,
    "low": 44.91,
    "index": 832
  },
  {
    "date": "2024-05-16",
    "open": 44.95,
    "close": 46.12,
    "high": 46.89,
    "low": 44.68,
    "index": 833
  },
  {
    "date": "2024-05-17",
    "open": 45.98,
    "close": 46.83,
    "high": 47.29,
    "low": 45.31,
    "index": 834
  },
  {
    "date": "2024-05-20",
    "open": 47.9,
    "close": 50.02,
    "high": 50.46,
    "low": 47.88,
    "index": 835
  },
  {
    "date": "2024-05-21",
    "open": 49.63,
    "close": 50.26,
    "high": 50.93,
    "low": 48.81,
    "index": 836
  },
  {
    "date": "2024-05-22",
    "open": 50.07,
    "close": 50.94,
    "high": 53.6,
    "low": 49.95,
    "index": 837
  },
  {
    "date": "2024-05-23",
    "open": 50.72,
    "close": 49.78,
    "high": 51.6,
    "low": 49.72,
    "index": 838
  },
  {
    "date": "2024-05-24",
    "open": 49.58,
    "close": 48.18,
    "high": 49.96,
    "low": 47.99,
    "index": 839
  },
  {
    "date": "2024-05-27",
    "open": 47.72,
    "close": 47.33,
    "high": 47.92,
    "low": 45.96,
    "index": 840
  },
  {
    "date": "2024-05-28",
    "open": 46.77,
    "close": 46.71,
    "high": 47.87,
    "low": 46.43,
    "index": 841
  },
  {
    "date": "2024-05-29",
    "open": 46.22,
    "close": 46.46,
    "high": 47.0,
    "low": 45.7,
    "index": 842
  },
  {
    "date": "2024-05-30",
    "open": 47.71,
    "close": 51.13,
    "high": 51.13,
    "low": 47.71,
    "index": 843
  },
  {
    "date": "2024-05-31",
    "open": 51.12,
    "close": 51.79,
    "high": 53.25,
    "low": 50.81,
    "index": 844
  },
  {
    "date": "2024-06-03",
    "open": 51.42,
    "close": 52.64,
    "high": 53.57,
    "low": 51.02,
    "index": 845
  },
  {
    "date": "2024-06-04",
    "open": 52.71,
    "close": 53.17,
    "high": 54.59,
    "low": 52.22,
    "index": 846
  },
  {
    "date": "2024-06-05",
    "open": 52.81,
    "close": 52.29,
    "high": 54.22,
    "low": 52.28,
    "index": 847
  },
  {
    "date": "2024-06-06",
    "open": 52.29,
    "close": 51.24,
    "high": 52.92,
    "low": 50.92,
    "index": 848
  },
  {
    "date": "2024-06-07",
    "open": 51.9,
    "close": 50.29,
    "high": 52.18,
    "low": 49.62,
    "index": 849
  },
  {
    "date": "2024-06-11",
    "open": 50.3,
    "close": 53.01,
    "high": 53.39,
    "low": 49.44,
    "index": 850
  },
  {
    "date": "2024-06-12",
    "open": 53.54,
    "close": 52.79,
    "high": 54.84,
    "low": 52.7,
    "index": 851
  },
  {
    "date": "2024-06-13",
    "open": 52.72,
    "close": 54.13,
    "high": 55.1,
    "low": 52.42,
    "index": 852
  },
  {
    "date": "2024-06-14",
    "open": 51.72,
    "close": 52.09,
    "high": 52.57,
    "low": 50.82,
    "index": 853
  },
  {
    "date": "2024-06-17",
    "open": 51.42,
    "close": 52.49,
    "high": 53.16,
    "low": 51.42,
    "index": 854
  },
  {
    "date": "2024-06-18",
    "open": 52.49,
    "close": 51.82,
    "high": 53.07,
    "low": 51.29,
    "index": 855
  },
  {
    "date": "2024-06-19",
    "open": 51.72,
    "close": 50.58,
    "high": 51.8,
    "low": 50.52,
    "index": 856
  },
  {
    "date": "2024-06-20",
    "open": 50.57,
    "close": 48.55,
    "high": 50.69,
    "low": 48.55,
    "index": 857
  },
  {
    "date": "2024-06-21",
    "open": 48.56,
    "close": 49.0,
    "high": 49.49,
    "low": 48.22,
    "index": 858
  },
  {
    "date": "2024-06-24",
    "open": 48.92,
    "close": 46.54,
    "high": 48.92,
    "low": 46.34,
    "index": 859
  },
  {
    "date": "2024-06-25",
    "open": 47.07,
    "close": 44.63,
    "high": 47.22,
    "low": 44.22,
    "index": 860
  },
  {
    "date": "2024-06-26",
    "open": 44.53,
    "close": 45.79,
    "high": 45.88,
    "low": 43.98,
    "index": 861
  },
  {
    "date": "2024-06-27",
    "open": 45.68,
    "close": 44.51,
    "high": 46.32,
    "low": 44.35,
    "index": 862
  },
  {
    "date": "2024-06-28",
    "open": 44.82,
    "close": 45.87,
    "high": 46.87,
    "low": 44.37,
    "index": 863
  },
  {
    "date": "2024-07-01",
    "open": 46.11,
    "close": 45.81,
    "high": 46.52,
    "low": 44.67,
    "index": 864
  },
  {
    "date": "2024-07-02",
    "open": 45.89,
    "close": 44.88,
    "high": 46.35,
    "low": 44.65,
    "index": 865
  },
  {
    "date": "2024-07-03",
    "open": 45.22,
    "close": 43.68,
    "high": 45.22,
    "low": 43.52,
    "index": 866
  },
  {
    "date": "2024-07-04",
    "open": 44.09,
    "close": 43.66,
    "high": 44.95,
    "low": 43.4,
    "index": 867
  },
  {
    "date": "2024-07-05",
    "open": 43.1,
    "close": 43.28,
    "high": 43.52,
    "low": 42.35,
    "index": 868
  },
  {
    "date": "2024-07-08",
    "open": 43.1,
    "close": 42.3,
    "high": 43.63,
    "low": 42.23,
    "index": 869
  },
  {
    "date": "2024-07-09",
    "open": 42.29,
    "close": 44.54,
    "high": 44.81,
    "low": 42.02,
    "index": 870
  },
  {
    "date": "2024-07-10",
    "open": 44.64,
    "close": 44.5,
    "high": 45.02,
    "low": 44.23,
    "index": 871
  },
  {
    "date": "2024-07-11",
    "open": 45.27,
    "close": 45.14,
    "high": 45.42,
    "low": 44.33,
    "index": 872
  },
  {
    "date": "2024-07-12",
    "open": 44.81,
    "close": 44.22,
    "high": 44.81,
    "low": 43.75,
    "index": 873
  },
  {
    "date": "2024-07-15",
    "open": 43.32,
    "close": 43.54,
    "high": 44.15,
    "low": 42.85,
    "index": 874
  },
  {
    "date": "2024-07-16",
    "open": 43.26,
    "close": 44.34,
    "high": 44.52,
    "low": 42.92,
    "index": 875
  },
  {
    "date": "2024-07-17",
    "open": 44.09,
    "close": 42.62,
    "high": 44.32,
    "low": 42.54,
    "index": 876
  },
  {
    "date": "2024-07-18",
    "open": 42.17,
    "close": 41.68,
    "high": 42.32,
    "low": 41.12,
    "index": 877
  },
  {
    "date": "2024-07-19",
    "open": 41.61,
    "close": 41.73,
    "high": 42.7,
    "low": 41.38,
    "index": 878
  },
  {
    "date": "2024-07-22",
    "open": 41.73,
    "close": 41.71,
    "high": 41.95,
    "low": 41.12,
    "index": 879
  },
  {
    "date": "2024-07-23",
    "open": 42.53,
    "close": 40.41,
    "high": 42.86,
    "low": 40.34,
    "index": 880
  },
  {
    "date": "2024-07-24",
    "open": 40.0,
    "close": 39.65,
    "high": 40.62,
    "low": 39.54,
    "index": 881
  },
  {
    "date": "2024-07-25",
    "open": 39.62,
    "close": 39.83,
    "high": 40.55,
    "low": 38.93,
    "index": 882
  },
  {
    "date": "2024-07-26",
    "open": 39.69,
    "close": 40.37,
    "high": 40.84,
    "low": 39.46,
    "index": 883
  },
  {
    "date": "2024-07-29",
    "open": 40.37,
    "close": 40.24,
    "high": 40.66,
    "low": 39.77,
    "index": 884
  },
  {
    "date": "2024-07-30",
    "open": 39.74,
    "close": 40.42,
    "high": 41.92,
    "low": 39.4,
    "index": 885
  },
  {
    "date": "2024-07-31",
    "open": 40.42,
    "close": 44.49,
    "high": 44.49,
    "low": 39.84,
    "index": 886
  },
  {
    "date": "2024-08-01",
    "open": 44.47,
    "close": 43.58,
    "high": 45.02,
    "low": 43.21,
    "index": 887
  },
  {
    "date": "2024-08-02",
    "open": 43.13,
    "close": 42.29,
    "high": 43.97,
    "low": 41.94,
    "index": 888
  },
  {
    "date": "2024-08-05",
    "open": 42.21,
    "close": 41.6,
    "high": 44.05,
    "low": 41.58,
    "index": 889
  },
  {
    "date": "2024-08-06",
    "open": 42.33,
    "close": 42.07,
    "high": 42.7,
    "low": 41.32,
    "index": 890
  },
  {
    "date": "2024-08-07",
    "open": 41.83,
    "close": 42.03,
    "high": 42.52,
    "low": 41.67,
    "index": 891
  },
  {
    "date": "2024-08-08",
    "open": 41.76,
    "close": 41.09,
    "high": 42.02,
    "low": 40.22,
    "index": 892
  },
  {
    "date": "2024-08-09",
    "open": 41.65,
    "close": 40.72,
    "high": 41.67,
    "low": 40.72,
    "index": 893
  },
  {
    "date": "2024-08-12",
    "open": 40.5,
    "close": 39.61,
    "high": 40.81,
    "low": 39.41,
    "index": 894
  },
  {
    "date": "2024-08-13",
    "open": 39.4,
    "close": 40.58,
    "high": 40.91,
    "low": 39.4,
    "index": 895
  },
  {
    "date": "2024-08-14",
    "open": 40.58,
    "close": 40.39,
    "high": 41.22,
    "low": 39.77,
    "index": 896
  },
  {
    "date": "2024-08-15",
    "open": 40.39,
    "close": 41.27,
    "high": 42.07,
    "low": 40.14,
    "index": 897
  },
  {
    "date": "2024-08-16",
    "open": 41.48,
    "close": 41.79,
    "high": 42.9,
    "low": 41.48,
    "index": 898
  },
  {
    "date": "2024-08-19",
    "open": 41.22,
    "close": 42.25,
    "high": 43.1,
    "low": 41.22,
    "index": 899
  },
  {
    "date": "2024-08-20",
    "open": 42.02,
    "close": 40.87,
    "high": 42.02,
    "low": 40.71,
    "index": 900
  },
  {
    "date": "2024-08-21",
    "open": 40.91,
    "close": 40.55,
    "high": 41.68,
    "low": 40.22,
    "index": 901
  },
  {
    "date": "2024-08-22",
    "open": 40.54,
    "close": 39.36,
    "high": 40.54,
    "low": 39.21,
    "index": 902
  },
  {
    "date": "2024-08-23",
    "open": 39.32,
    "close": 38.9,
    "high": 39.54,
    "low": 38.67,
    "index": 903
  },
  {
    "date": "2024-08-26",
    "open": 38.32,
    "close": 38.07,
    "high": 39.1,
    "low": 37.74,
    "index": 904
  },
  {
    "date": "2024-08-27",
    "open": 37.84,
    "close": 37.32,
    "high": 37.87,
    "low": 36.92,
    "index": 905
  },
  {
    "date": "2024-08-28",
    "open": 37.53,
    "close": 36.66,
    "high": 37.62,
    "low": 35.62,
    "index": 906
  },
  {
    "date": "2024-08-29",
    "open": 36.15,
    "close": 37.17,
    "high": 37.7,
    "low": 36.04,
    "index": 907
  },
  {
    "date": "2024-08-30",
    "open": 37.13,
    "close": 38.08,
    "high": 38.81,
    "low": 36.93,
    "index": 908
  },
  {
    "date": "2024-09-02",
    "open": 38.05,
    "close": 36.17,
    "high": 38.3,
    "low": 36.07,
    "index": 909
  },
  {
    "date": "2024-09-03",
    "open": 36.17,
    "close": 36.74,
    "high": 37.22,
    "low": 36.16,
    "index": 910
  },
  {
    "date": "2024-09-04",
    "open": 36.49,
    "close": 35.65,
    "high": 36.49,
    "low": 35.58,
    "index": 911
  },
  {
    "date": "2024-09-05",
    "open": 35.76,
    "close": 36.02,
    "high": 36.15,
    "low": 35.53,
    "index": 912
  },
  {
    "date": "2024-09-06",
    "open": 36.02,
    "close": 35.43,
    "high": 36.3,
    "low": 35.4,
    "index": 913
  },
  {
    "date": "2024-09-09",
    "open": 35.25,
    "close": 35.27,
    "high": 35.8,
    "low": 35.02,
    "index": 914
  },
  {
    "date": "2024-09-10",
    "open": 35.36,
    "close": 35.64,
    "high": 35.88,
    "low": 34.61,
    "index": 915
  },
  {
    "date": "2024-09-11",
    "open": 35.65,
    "close": 36.0,
    "high": 36.48,
    "low": 35.24,
    "index": 916
  },
  {
    "date": "2024-09-12",
    "open": 36.0,
    "close": 35.07,
    "high": 36.27,
    "low": 35.02,
    "index": 917
  },
  {
    "date": "2024-09-13",
    "open": 34.97,
    "close": 34.43,
    "high": 35.27,
    "low": 34.43,
    "index": 918
  },
  {
    "date": "2024-09-18",
    "open": 34.32,
    "close": 34.99,
    "high": 35.35,
    "low": 33.93,
    "index": 919
  },
  {
    "date": "2024-09-19",
    "open": 35.14,
    "close": 35.2,
    "high": 35.96,
    "low": 34.44,
    "index": 920
  },
  {
    "date": "2024-09-20",
    "open": 35.38,
    "close": 34.53,
    "high": 35.47,
    "low": 34.25,
    "index": 921
  },
  {
    "date": "2024-09-23",
    "open": 34.41,
    "close": 34.33,
    "high": 35.0,
    "low": 34.15,
    "index": 922
  },
  {
    "date": "2024-09-24",
    "open": 34.52,
    "close": 35.89,
    "high": 35.89,
    "low": 33.99,
    "index": 923
  },
  {
    "date": "2024-09-25",
    "open": 36.32,
    "close": 35.84,
    "high": 37.34,
    "low": 35.83,
    "index": 924
  },
  {
    "date": "2024-09-26",
    "open": 35.8,
    "close": 38.84,
    "high": 38.96,
    "low": 35.73,
    "index": 925
  },
  {
    "date": "2024-09-27",
    "open": 39.47,
    "close": 41.43,
    "high": 42.49,
    "low": 39.12,
    "index": 926
  },
  {
    "date": "2024-09-30",
    "open": 42.6,
    "close": 44.64,
    "high": 44.89,
    "low": 41.72,
    "index": 927
  },
  {
    "date": "2024-10-08",
    "open": 49.13,
    "close": 47.93,
    "high": 49.13,
    "low": 43.59,
    "index": 928
  },
  {
    "date": "2024-10-09",
    "open": 46.72,
    "close": 48.93,
    "high": 52.05,
    "low": 44.72,
    "index": 929
  },
  {
    "date": "2024-10-10",
    "open": 49.27,
    "close": 46.02,
    "high": 49.52,
    "low": 45.9,
    "index": 930
  },
  {
    "date": "2024-10-11",
    "open": 45.1,
    "close": 42.47,
    "high": 45.62,
    "low": 41.82,
    "index": 931
  },
  {
    "date": "2024-10-14",
    "open": 42.47,
    "close": 46.49,
    "high": 46.75,
    "low": 40.97,
    "index": 932
  },
  {
    "date": "2024-10-15",
    "open": 45.55,
    "close": 44.13,
    "high": 46.2,
    "low": 44.13,
    "index": 933
  },
  {
    "date": "2024-10-16",
    "open": 42.73,
    "close": 43.68,
    "high": 44.71,
    "low": 42.73,
    "index": 934
  },
  {
    "date": "2024-10-17",
    "open": 43.72,
    "close": 44.19,
    "high": 45.72,
    "low": 43.72,
    "index": 935
  },
  {
    "date": "2024-10-18",
    "open": 44.09,
    "close": 46.81,
    "high": 47.94,
    "low": 43.9,
    "index": 936
  },
  {
    "date": "2024-10-21",
    "open": 47.04,
    "close": 46.65,
    "high": 48.48,
    "low": 46.13,
    "index": 937
  },
  {
    "date": "2024-10-22",
    "open": 46.44,
    "close": 47.87,
    "high": 49.55,
    "low": 46.12,
    "index": 938
  },
  {
    "date": "2024-10-23",
    "open": 47.52,
    "close": 46.22,
    "high": 47.67,
    "low": 45.92,
    "index": 939
  },
  {
    "date": "2024-10-24",
    "open": 46.21,
    "close": 45.82,
    "high": 46.48,
    "low": 45.3,
    "index": 940
  },
  {
    "date": "2024-10-25",
    "open": 46.52,
    "close": 45.92,
    "high": 46.62,
    "low": 45.52,
    "index": 941
  },
  {
    "date": "2024-10-28",
    "open": 45.93,
    "close": 45.94,
    "high": 46.38,
    "low": 45.18,
    "index": 942
  },
  {
    "date": "2024-10-29",
    "open": 46.82,
    "close": 47.1,
    "high": 49.01,
    "low": 46.42,
    "index": 943
  },
  {
    "date": "2024-10-30",
    "open": 46.52,
    "close": 46.61,
    "high": 48.1,
    "low": 45.91,
    "index": 944
  },
  {
    "date": "2024-10-31",
    "open": 46.52,
    "close": 46.06,
    "high": 46.52,
    "low": 45.49,
    "index": 945
  },
  {
    "date": "2024-11-01",
    "open": 45.76,
    "close": 44.13,
    "high": 45.97,
    "low": 43.92,
    "index": 946
  },
  {
    "date": "2024-11-04",
    "open": 45.42,
    "close": 47.74,
    "high": 48.55,
    "low": 45.42,
    "index": 947
  },
  {
    "date": "2024-11-05",
    "open": 47.44,
    "close": 52.54,
    "high": 52.54,
    "low": 46.75,
    "index": 948
  },
  {
    "date": "2024-11-06",
    "open": 57.78,
    "close": 55.33,
    "high": 57.82,
    "low": 54.78,
    "index": 949
  },
  {
    "date": "2024-11-07",
    "open": 57.78,
    "close": 54.7,
    "high": 58.01,
    "low": 52.89,
    "index": 950
  },
  {
    "date": "2024-11-08",
    "open": 54.52,
    "close": 56.0,
    "high": 59.1,
    "low": 54.32,
    "index": 951
  },
  {
    "date": "2024-11-11",
    "open": 54.92,
    "close": 61.63,
    "high": 61.63,
    "low": 54.92,
    "index": 952
  },
  {
    "date": "2024-11-12",
    "open": 61.52,
    "close": 59.2,
    "high": 61.67,
    "low": 58.5,
    "index": 953
  },
  {
    "date": "2024-11-13",
    "open": 59.97,
    "close": 65.15,
    "high": 65.15,
    "low": 59.18,
    "index": 954
  },
  {
    "date": "2024-11-14",
    "open": 66.69,
    "close": 64.29,
    "high": 70.72,
    "low": 64.18,
    "index": 955
  },
  {
    "date": "2024-11-15",
    "open": 64.92,
    "close": 61.54,
    "high": 69.22,
    "low": 59.27,
    "index": 956
  },
  {
    "date": "2024-11-18",
    "open": 61.52,
    "close": 57.67,
    "high": 65.39,
    "low": 57.22,
    "index": 957
  },
  {
    "date": "2024-11-19",
    "open": 56.68,
    "close": 63.47,
    "high": 63.47,
    "low": 56.65,
    "index": 958
  },
  {
    "date": "2024-11-20",
    "open": 63.72,
    "close": 69.85,
    "high": 69.85,
    "low": 63.22,
    "index": 959
  },
  {
    "date": "2024-11-21",
    "open": 69.85,
    "close": 66.02,
    "high": 69.9,
    "low": 64.93,
    "index": 960
  },
  {
    "date": "2024-11-22",
    "open": 65.72,
    "close": 70.57,
    "high": 72.65,
    "low": 65.04,
    "index": 961
  },
  {
    "date": "2024-11-25",
    "open": 71.61,
    "close": 74.55,
    "high": 76.04,
    "low": 67.8,
    "index": 962
  },
  {
    "date": "2024-11-26",
    "open": 70.72,
    "close": 67.07,
    "high": 71.73,
    "low": 67.07,
    "index": 963
  },
  {
    "date": "2024-11-27",
    "open": 65.38,
    "close": 67.9,
    "high": 68.6,
    "low": 65.14,
    "index": 964
  },
  {
    "date": "2024-11-28",
    "open": 66.72,
    "close": 67.08,
    "high": 69.72,
    "low": 66.24,
    "index": 965
  },
  {
    "date": "2024-11-29",
    "open": 68.73,
    "close": 73.82,
    "high": 73.82,
    "low": 68.73,
    "index": 966
  },
  {
    "date": "2024-12-02",
    "open": 76.73,
    "close": 79.34,
    "high": 81.23,
    "low": 76.72,
    "index": 967
  },
  {
    "date": "2024-12-03",
    "open": 79.4,
    "close": 78.06,
    "high": 81.71,
    "low": 77.49,
    "index": 968
  },
  {
    "date": "2024-12-04",
    "open": 76.72,
    "close": 79.72,
    "high": 84.25,
    "low": 76.72,
    "index": 969
  },
  {
    "date": "2024-12-05",
    "open": 79.22,
    "close": 83.6,
    "high": 86.6,
    "low": 78.97,
    "index": 970
  },
  {
    "date": "2024-12-06",
    "open": 81.16,
    "close": 80.6,
    "high": 88.6,
    "low": 77.32,
    "index": 971
  },
  {
    "date": "2024-12-09",
    "open": 77.72,
    "close": 81.11,
    "high": 84.52,
    "low": 77.22,
    "index": 972
  },
  {
    "date": "2024-12-10",
    "open": 81.92,
    "close": 87.6,
    "high": 89.19,
    "low": 81.17,
    "index": 973
  },
  {
    "date": "2024-12-11",
    "open": 84.72,
    "close": 83.93,
    "high": 86.02,
    "low": 81.52,
    "index": 974
  },
  {
    "date": "2024-12-12",
    "open": 81.82,
    "close": 81.83,
    "high": 84.84,
    "low": 81.31,
    "index": 975
  },
  {
    "date": "2024-12-13",
    "open": 80.57,
    "close": 77.68,
    "high": 82.02,
    "low": 77.3,
    "index": 976
  },
  {
    "date": "2024-12-16",
    "open": 76.21,
    "close": 77.29,
    "high": 79.0,
    "low": 76.21,
    "index": 977
  },
  {
    "date": "2024-12-17",
    "open": 77.08,
    "close": 72.73,
    "high": 77.56,
    "low": 72.27,
    "index": 978
  },
  {
    "date": "2024-12-18",
    "open": 73.15,
    "close": 72.71,
    "high": 73.46,
    "low": 71.47,
    "index": 979
  },
  {
    "date": "2024-12-19",
    "open": 71.73,
    "close": 72.48,
    "high": 73.3,
    "low": 71.03,
    "index": 980
  },
  {
    "date": "2024-12-20",
    "open": 71.73,
    "close": 74.31,
    "high": 76.29,
    "low": 71.62,
    "index": 981
  },
  {
    "date": "2024-12-23",
    "open": 73.92,
    "close": 71.3,
    "high": 73.92,
    "low": 70.76,
    "index": 982
  },
  {
    "date": "2024-12-24",
    "open": 73.62,
    "close": 74.14,
    "high": 75.71,
    "low": 72.53,
    "index": 983
  },
  {
    "date": "2024-12-25",
    "open": 74.15,
    "close": 72.32,
    "high": 75.01,
    "low": 71.79,
    "index": 984
  },
  {
    "date": "2024-12-26",
    "open": 72.22,
    "close": 77.2,
    "high": 77.29,
    "low": 71.71,
    "index": 985
  },
  {
    "date": "2024-12-27",
    "open": 76.94,
    "close": 75.2,
    "high": 78.72,
    "low": 74.27,
    "index": 986
  },
  {
    "date": "2024-12-30",
    "open": 75.21,
    "close": 76.6,
    "high": 77.72,
    "low": 73.45,
    "index": 987
  },
  {
    "date": "2024-12-31",
    "open": 76.34,
    "close": 73.63,
    "high": 76.9,
    "low": 73.5,
    "index": 988
  },
  {
    "date": "2025-01-02",
    "open": 73.61,
    "close": 74.7,
    "high": 76.69,
    "low": 71.68,
    "index": 989
  },
  {
    "date": "2025-01-03",
    "open": 74.72,
    "close": 69.77,
    "high": 74.78,
    "low": 69.52,
    "index": 990
  },
  {
    "date": "2025-01-06",
    "open": 68.87,
    "close": 68.3,
    "high": 69.91,
    "low": 67.42,
    "index": 991
  },
  {
    "date": "2025-01-07",
    "open": 68.51,
    "close": 70.92,
    "high": 70.92,
    "low": 68.22,
    "index": 992
  },
  {
    "date": "2025-01-08",
    "open": 70.76,
    "close": 78.04,
    "high": 78.04,
    "low": 69.9,
    "index": 993
  },
  {
    "date": "2025-01-09",
    "open": 78.15,
    "close": 85.87,
    "high": 85.87,
    "low": 77.64,
    "index": 994
  },
  {
    "date": "2025-01-10",
    "open": 87.22,
    "close": 91.72,
    "high": 94.49,
    "low": 86.39,
    "index": 995
  },
  {
    "date": "2025-01-13",
    "open": 90.22,
    "close": 85.71,
    "high": 91.72,
    "low": 82.92,
    "index": 996
  },
  {
    "date": "2025-01-14",
    "open": 84.28,
    "close": 93.25,
    "high": 93.25,
    "low": 83.82,
    "index": 997
  },
  {
    "date": "2025-01-15",
    "open": 89.71,
    "close": 97.64,
    "high": 99.19,
    "low": 88.87,
    "index": 998
  },
  {
    "date": "2025-01-16",
    "open": 96.72,
    "close": 95.83,
    "high": 102.02,
    "low": 94.02,
    "index": 999
  },
  {
    "date": "2025-01-17",
    "open": 94.87,
    "close": 94.22,
    "high": 96.13,
    "low": 92.02,
    "index": 1000
  },
  {
    "date": "2025-01-20",
    "open": 94.72,
    "close": 95.14,
    "high": 96.09,
    "low": 93.06,
    "index": 1001
  },
  {
    "date": "2025-01-21",
    "open": 95.23,
    "close": 104.68,
    "high": 104.68,
    "low": 93.87,
    "index": 1002
  },
  {
    "date": "2025-01-22",
    "open": 103.72,
    "close": 100.44,
    "high": 104.48,
    "low": 99.15,
    "index": 1003
  },
  {
    "date": "2025-01-23",
    "open": 100.52,
    "close": 97.34,
    "high": 103.22,
    "low": 96.69,
    "index": 1004
  },
  {
    "date": "2025-01-24",
    "open": 95.52,
    "close": 100.59,
    "high": 101.21,
    "low": 94.62,
    "index": 1005
  },
  {
    "date": "2025-01-27",
    "open": 100.52,
    "close": 92.97,
    "high": 100.58,
    "low": 92.63,
    "index": 1006
  },
  {
    "date": "2025-02-05",
    "open": 95.88,
    "close": 100.7,
    "high": 102.3,
    "low": 95.88,
    "index": 1007
  },
  {
    "date": "2025-02-06",
    "open": 99.71,
    "close": 110.8,
    "high": 110.8,
    "low": 99.6,
    "index": 1008
  },
  {
    "date": "2025-02-07",
    "open": 109.72,
    "close": 111.45,
    "high": 114.85,
    "low": 107.72,
    "index": 1009
  },
  {
    "date": "2025-02-10",
    "open": 108.94,
    "close": 113.54,
    "high": 115.85,
    "low": 107.05,
    "index": 1010
  },
  {
    "date": "2025-02-11",
    "open": 111.18,
    "close": 118.01,
    "high": 122.4,
    "low": 110.72,
    "index": 1011
  },
  {
    "date": "2025-02-12",
    "open": 116.58,
    "close": 129.7,
    "high": 129.84,
    "low": 115.22,
    "index": 1012
  },
  {
    "date": "2025-02-13",
    "open": 125.8,
    "close": 116.7,
    "high": 127.14,
    "low": 116.7,
    "index": 1013
  },
  {
    "date": "2025-02-14",
    "open": 112.72,
    "close": 112.92,
    "high": 116.7,
    "low": 111.72,
    "index": 1014
  },
  {
    "date": "2025-02-17",
    "open": 111.72,
    "close": 120.04,
    "high": 121.14,
    "low": 111.72,
    "index": 1015
  },
  {
    "date": "2025-02-18",
    "open": 117.76,
    "close": 116.32,
    "high": 122.29,
    "low": 114.88,
    "index": 1016
  },
  {
    "date": "2025-02-19",
    "open": 114.72,
    "close": 125.78,
    "high": 126.72,
    "low": 114.52,
    "index": 1017
  },
  {
    "date": "2025-02-20",
    "open": 125.3,
    "close": 131.18,
    "high": 136.72,
    "low": 121.72,
    "index": 1018
  },
  {
    "date": "2025-02-21",
    "open": 127.72,
    "close": 132.32,
    "high": 134.97,
    "low": 127.32,
    "index": 1019
  },
  {
    "date": "2025-02-24",
    "open": 129.81,
    "close": 130.46,
    "high": 137.9,
    "low": 122.72,
    "index": 1020
  },
  {
    "date": "2025-02-25",
    "open": 125.72,
    "close": 143.3,
    "high": 143.53,
    "low": 125.72,
    "index": 1021
  },
  {
    "date": "2025-02-26",
    "open": 141.43,
    "close": 157.66,
    "high": 157.66,
    "low": 141.05,
    "index": 1022
  },
  {
    "date": "2025-02-27",
    "open": 156.7,
    "close": 156.72,
    "high": 163.71,
    "low": 149.73,
    "index": 1023
  },
  {
    "date": "2025-02-28",
    "open": 154.72,
    "close": 141.02,
    "high": 155.11,
    "low": 141.02,
    "index": 1024
  },
  {
    "date": "2025-03-03",
    "open": 139.72,
    "close": 134.17,
    "high": 140.94,
    "low": 132.64,
    "index": 1025
  },
  {
    "date": "2025-03-04",
    "open": 135.71,
    "close": 147.52,
    "high": 147.62,
    "low": 135.65,
    "index": 1026
  },
  {
    "date": "2025-03-05",
    "open": 145.75,
    "close": 162.3,
    "high": 162.3,
    "low": 144.03,
    "index": 1027
  },
  {
    "date": "2025-03-06",
    "open": 162.02,
    "close": 159.92,
    "high": 168.49,
    "low": 157.82,
    "index": 1028
  },
  {
    "date": "2025-03-07",
    "open": 159.97,
    "close": 158.52,
    "high": 164.72,
    "low": 155.98,
    "index": 1029
  },
  {
    "date": "2025-03-10",
    "open": 159.72,
    "close": 160.89,
    "high": 164.67,
    "low": 155.02,
    "index": 1030
  },
  {
    "date": "2025-03-11",
    "open": 156.72,
    "close": 156.82,
    "high": 162.01,
    "low": 149.72,
    "index": 1031
  },
  {
    "date": "2025-03-12",
    "open": 155.93,
    "close": 155.88,
    "high": 158.01,
    "low": 154.22,
    "index": 1032
  },
  {
    "date": "2025-03-13",
    "open": 154.03,
    "close": 144.35,
    "high": 154.87,
    "low": 142.6,
    "index": 1033
  },
  {
    "date": "2025-03-14",
    "open": 147.22,
    "close": 147.38,
    "high": 149.99,
    "low": 142.82,
    "index": 1034
  },
  {
    "date": "2025-03-17",
    "open": 145.72,
    "close": 149.37,
    "high": 151.74,
    "low": 142.87,
    "index": 1035
  },
  {
    "date": "2025-03-18",
    "open": 148.55,
    "close": 145.67,
    "high": 150.33,
    "low": 143.73,
    "index": 1036
  },
  {
    "date": "2025-03-19",
    "open": 144.27,
    "close": 142.63,
    "high": 146.65,
    "low": 141.92,
    "index": 1037
  },
  {
    "date": "2025-03-20",
    "open": 142.32,
    "close": 144.33,
    "high": 149.42,
    "low": 140.46,
    "index": 1038
  },
  {
    "date": "2025-03-21",
    "open": 141.59,
    "close": 134.97,
    "high": 142.46,
    "low": 132.78,
    "index": 1039
  },
  {
    "date": "2025-03-24",
    "open": 134.97,
    "close": 134.31,
    "high": 136.82,
    "low": 130.11,
    "index": 1040
  },
  {
    "date": "2025-03-25",
    "open": 134.72,
    "close": 130.86,
    "high": 137.7,
    "low": 130.61,
    "index": 1041
  },
  {
    "date": "2025-03-26",
    "open": 130.42,
    "close": 132.27,
    "high": 136.95,
    "low": 130.26,
    "index": 1042
  },
  {
    "date": "2025-03-27",
    "open": 132.38,
    "close": 130.51,
    "high": 134.42,
    "low": 130.32,
    "index": 1043
  },
  {
    "date": "2025-03-28",
    "open": 129.23,
    "close": 130.45,
    "high": 132.89,
    "low": 127.62,
    "index": 1044
  },
  {
    "date": "2025-03-31",
    "open": 126.72,
    "close": 127.55,
    "high": 128.72,
    "low": 121.55,
    "index": 1045
  },
  {
    "date": "2025-04-01",
    "open": 128.6,
    "close": 126.27,
    "high": 129.48,
    "low": 124.04,
    "index": 1046
  },
  {
    "date": "2025-04-02",
    "open": 125.6,
    "close": 126.42,
    "high": 129.0,
    "low": 125.0,
    "index": 1047
  },
  {
    "date": "2025-04-03",
    "open": 124.42,
    "close": 121.92,
    "high": 127.22,
    "low": 121.44,
    "index": 1048
  },
  {
    "date": "2025-04-07",
    "open": 109.7,
    "close": 109.7,
    "high": 109.7,
    "low": 109.7,
    "index": 1049
  },
  {
    "date": "2025-04-08",
    "open": 98.7,
    "close": 98.7,
    "high": 103.22,
    "low": 98.7,
    "index": 1050
  },
  {
    "date": "2025-04-09",
    "open": 93.21,
    "close": 98.72,
    "high": 102.72,
    "low": 88.8,
    "index": 1051
  },
  {
    "date": "2025-04-10",
    "open": 103.72,
    "close": 102.94,
    "high": 107.0,
    "low": 100.72,
    "index": 1052
  },
  {
    "date": "2025-04-11",
    "open": 100.79,
    "close": 113.26,
    "high": 113.26,
    "low": 99.83,
    "index": 1053
  },
  {
    "date": "2025-04-14",
    "open": 116.6,
    "close": 113.97,
    "high": 118.44,
    "low": 113.22,
    "index": 1054
  },
  {
    "date": "2025-04-15",
    "open": 114.72,
    "close": 113.13,
    "high": 116.01,
    "low": 111.42,
    "index": 1055
  },
  {
    "date": "2025-04-16",
    "open": 112.23,
    "close": 112.49,
    "high": 116.19,
    "low": 111.32,
    "index": 1056
  },
  {
    "date": "2025-04-17",
    "open": 111.33,
    "close": 111.05,
    "high": 115.58,
    "low": 110.8,
    "index": 1057
  },
  {
    "date": "2025-04-18",
    "open": 110.83,
    "close": 109.27,
    "high": 112.11,
    "low": 108.42,
    "index": 1058
  },
  {
    "date": "2025-04-21",
    "open": 107.83,
    "close": 115.72,
    "high": 116.55,
    "low": 106.41,
    "index": 1059
  },
  {
    "date": "2025-04-22",
    "open": 115.72,
    "close": 113.71,
    "high": 117.82,
    "low": 113.63,
    "index": 1060
  },
  {
    "date": "2025-04-23",
    "open": 117.48,
    "close": 125.11,
    "high": 125.11,
    "low": 116.23,
    "index": 1061
  },
  {
    "date": "2025-04-24",
    "open": 127.22,
    "close": 125.04,
    "high": 131.2,
    "low": 124.34,
    "index": 1062
  },
  {
    "date": "2025-04-25",
    "open": 127.21,
    "close": 127.07,
    "high": 130.4,
    "low": 123.16,
    "index": 1063
  },
  {
    "date": "2025-04-28",
    "open": 127.07,
    "close": 123.26,
    "high": 128.7,
    "low": 122.97,
    "index": 1064
  },
  {
    "date": "2025-04-29",
    "open": 121.83,
    "close": 124.72,
    "high": 127.11,
    "low": 119.73,
    "index": 1065
  },
  {
    "date": "2025-04-30",
    "open": 124.29,
    "close": 126.78,
    "high": 128.69,
    "low": 122.8,
    "index": 1066
  },
  {
    "date": "2025-05-06",
    "open": 128.69,
    "close": 130.23,
    "high": 132.46,
    "low": 125.72,
    "index": 1067
  },
  {
    "date": "2025-05-07",
    "open": 131.86,
    "close": 127.77,
    "high": 132.34,
    "low": 124.97,
    "index": 1068
  },
  {
    "date": "2025-05-08",
    "open": 126.53,
    "close": 126.71,
    "high": 128.72,
    "low": 125.39,
    "index": 1069
  },
  {
    "date": "2025-05-09",
    "open": 126.72,
    "close": 123.72,
    "high": 126.72,
    "low": 121.94,
    "index": 1070
  },
  {
    "date": "2025-05-12",
    "open": 125.72,
    "close": 133.22,
    "high": 135.7,
    "low": 124.72,
    "index": 1071
  },
  {
    "date": "2025-05-13",
    "open": 129.72,
    "close": 125.32,
    "high": 130.72,
    "low": 124.72,
    "index": 1072
  },
  {
    "date": "2025-05-14",
    "open": 124.08,
    "close": 124.36,
    "high": 125.52,
    "low": 122.72,
    "index": 1073
  },
  {
    "date": "2025-05-15",
    "open": 123.92,
    "close": 119.85,
    "high": 123.92,
    "low": 119.72,
    "index": 1074
  },
  {
    "date": "2025-05-16",
    "open": 118.69,
    "close": 120.64,
    "high": 123.06,
    "low": 117.92,
    "index": 1075
  },
  {
    "date": "2025-05-19",
    "open": 119.43,
    "close": 115.07,
    "high": 119.72,
    "low": 113.22,
    "index": 1076
  },
  {
    "date": "2025-05-20",
    "open": 115.71,
    "close": 114.77,
    "high": 116.09,
    "low": 113.85,
    "index": 1077
  },
  {
    "date": "2025-05-21",
    "open": 113.87,
    "close": 113.49,
    "high": 115.1,
    "low": 112.73,
    "index": 1078
  },
  {
    "date": "2025-05-22",
    "open": 113.46,
    "close": 112.32,
    "high": 114.61,
    "low": 111.83,
    "index": 1079
  },
  {
    "date": "2025-05-23",
    "open": 111.52,
    "close": 111.46,
    "high": 114.52,
    "low": 110.93,
    "index": 1080
  },
  {
    "date": "2025-05-26",
    "open": 110.84,
    "close": 110.57,
    "high": 112.02,
    "low": 109.72,
    "index": 1081
  },
  {
    "date": "2025-05-27",
    "open": 110.62,
    "close": 107.44,
    "high": 110.98,
    "low": 107.03,
    "index": 1082
  },
  {
    "date": "2025-05-28",
    "open": 107.3,
    "close": 106.34,
    "high": 108.61,
    "low": 105.88,
    "index": 1083
  },
  {
    "date": "2025-05-29",
    "open": 106.65,
    "close": 107.28,
    "high": 108.5,
    "low": 106.65,
    "index": 1084
  },
  {
    "date": "2025-05-30",
    "open": 107.0,
    "close": 103.06,
    "high": 107.0,
    "low": 102.68,
    "index": 1085
  },
  {
    "date": "2025-06-03",
    "open": 102.0,
    "close": 102.9,
    "high": 104.38,
    "low": 101.1,
    "index": 1086
  },
  {
    "date": "2025-06-04",
    "open": 102.9,
    "close": 103.4,
    "high": 103.92,
    "low": 101.8,
    "index": 1087
  },
  {
    "date": "2025-06-05",
    "open": 103.4,
    "close": 104.31,
    "high": 105.7,
    "low": 102.33,
    "index": 1088
  },
  {
    "date": "2025-06-06",
    "open": 104.66,
    "close": 103.09,
    "high": 104.69,
    "low": 102.07,
    "index": 1089
  },
  {
    "date": "2025-06-09",
    "open": 103.87,
    "close": 104.5,
    "high": 109.3,
    "low": 103.82,
    "index": 1090
  },
  {
    "date": "2025-06-10",
    "open": 104.5,
    "close": 101.23,
    "high": 105.0,
    "low": 98.58,
    "index": 1091
  },
  {
    "date": "2025-06-11",
    "open": 101.22,
    "close": 101.96,
    "high": 102.5,
    "low": 100.23,
    "index": 1092
  },
  {
    "date": "2025-06-12",
    "open": 101.01,
    "close": 102.08,
    "high": 102.37,
    "low": 100.6,
    "index": 1093
  },
  {
    "date": "2025-06-13",
    "open": 101.01,
    "close": 100.09,
    "high": 103.69,
    "low": 99.49,
    "index": 1094
  },
  {
    "date": "2025-06-16",
    "open": 99.42,
    "close": 99.53,
    "high": 100.48,
    "low": 99.36,
    "index": 1095
  },
  {
    "date": "2025-06-17",
    "open": 99.98,
    "close": 99.01,
    "high": 100.8,
    "low": 98.26,
    "index": 1096
  },
  {
    "date": "2025-06-18",
    "open": 98.78,
    "close": 100.22,
    "high": 101.0,
    "low": 98.33,
    "index": 1097
  },
  {
    "date": "2025-06-19",
    "open": 100.05,
    "close": 103.08,
    "high": 105.84,
    "low": 99.11,
    "index": 1098
  },
  {
    "date": "2025-06-20",
    "open": 102.62,
    "close": 98.4,
    "high": 102.62,
    "low": 98.3,
    "index": 1099
  },
  {
    "date": "2025-06-23",
    "open": 96.0,
    "close": 98.12,
    "high": 99.17,
    "low": 95.59,
    "index": 1100
  },
  {
    "date": "2025-06-24",
    "open": 99.2,
    "close": 105.24,
    "high": 106.5,
    "low": 99.02,
    "index": 1101
  }
];
        const windowSize = 60;
        const turnPointThreshold = 10.0;

        // 当前状态
        let currentIndex = 0;
        let calculator = new StockTrendCalculator(windowSize, turnPointThreshold);
        let autoPlayInterval = null;

        // 交易记录跟踪
        let tradingRecords = [];
        let currentPosition = null; // {type: 'LONG', quantity: 100, entryPrice: 120.5, entryDate: '2024-01-01'}
        let cash = 100000; // 初始资金
        let tradeSequence = 0; // 交易序号
        let tradePairId = 0; // 交易对ID，用于标识买卖配对

        // 图表实例
        let chart = echarts.init(document.getElementById('chart'));

        // 更新图表
        function updateChart() {
            if (currentIndex === 0) {
                chart.clear();
                return;
            }

            const currentData = stockData.slice(0, currentIndex);
            const dates = currentData.map(d => d.date);
            const klineData = currentData.map(d => [d.open, d.close, d.low, d.high]);

            const [highLine, lowLine] = calculator.getCurrentTrendLines();
            const dayIndices = currentData.map((_, i) => i);

            let highTrends = calculator.getTrendLineValues(highLine, dayIndices);
            let lowTrends = calculator.getTrendLineValues(lowLine, dayIndices);
            let highSlope = highLine ? highLine.slope : 0;
            let lowSlope = lowLine ? lowLine.slope : 0;

            document.getElementById('currentDay').textContent = currentIndex;
            document.getElementById('highSlope').textContent = highSlope.toFixed(4);
            document.getElementById('lowSlope').textContent = lowSlope.toFixed(4);

            const peaks = calculator.turnPointDetector.getPeaks();
            const troughs = calculator.turnPointDetector.getTroughs();

            // 获取交易信号
            const tradingSignals = calculator.getTradingSignals();
            const buySignals = calculator.getBuySignals();
            const sellSignals = calculator.getSellSignals();

            const peakMarkers = peaks.map(p => ({
                name: '波峰',
                coord: [stockData[p.dayIndex].date, p.price],
                value: p.price.toFixed(2),
                itemStyle: { color: 'rgb(234, 85, 69)' }
            }));
            const troughMarkers = troughs.map(t => ({
                name: '波谷',
                coord: [stockData[t.dayIndex].date, t.price],
                value: t.price.toFixed(2),
                itemStyle: { color: 'rgb(69, 234, 135)' }
            }));

            // 醒目的买卖信号标记
            const buyMarkers = buySignals.map(b => ({
                name: '买入信号',
                coord: [stockData[b.dayIndex].date, b.price],
                value: '买入\n' + b.price.toFixed(2),
                symbol: 'triangle',
                symbolSize: 35,
                itemStyle: {
                    color: '#00FF00',
                    borderColor: '#008000',
                    borderWidth: 3
                },
                label: {
                    show: true,
                    position: 'bottom',
                    fontSize: 12,
                    fontWeight: 'bold',
                    color: '#FFFFFF',
                    backgroundColor: '#00AA00',
                    padding: [4, 8],
                    borderRadius: 4
                }
            }));

            const sellMarkers = sellSignals.map(s => ({
                name: '卖出信号',
                coord: [stockData[s.dayIndex].date, s.price],
                value: '卖出\n' + s.price.toFixed(2),
                symbol: 'triangle',
                symbolSize: 35,
                symbolRotate: 180,
                itemStyle: {
                    color: '#FF0000',
                    borderColor: '#800000',
                    borderWidth: 3
                },
                label: {
                    show: true,
                    position: 'top',
                    fontSize: 12,
                    fontWeight: 'bold',
                    color: '#FFFFFF',
                    backgroundColor: '#CC0000',
                    padding: [4, 8],
                    borderRadius: 4
                }
            }));

            const option = {
                title: {
                    text: `增强型股票趋势分析 (阈值: ${turnPointThreshold}%)`,
                    left: 'center'
                },
                tooltip: { trigger: 'axis', axisPointer: { type: 'cross' } },
                legend: { data: ['K线', '高点趋势线', '低点趋势线', '买入信号', '卖出信号'], top: 30 },
                grid: { left: '3%', right: '4%', bottom: '15%', top: '15%', containLabel: true },
                xAxis: { type: 'category', data: dates, scale: true, boundaryGap: false, axisLine: { onZero: false }, splitLine: { show: false }, min: 'dataMin', max: 'dataMax' },
                yAxis: { scale: true, splitArea: { show: true } },
                dataZoom: [ { type: 'inside', start: 0, end: 100 }, { show: true, type: 'slider', top: '90%', start: 0, end: 100 } ],
                series: [
                    {
                        name: 'K线',
                        type: 'candlestick',
                        data: klineData,
                        itemStyle: { color: '#ec0000', color0: '#00da3c', borderColor: '#8A0000', borderColor0: '#008F28' },
                        markPoint: {
                            symbol: 'pin',
                            symbolSize: 25,
                            label: { fontSize: 10, color: '#fff' },
                            data: [...peakMarkers, ...troughMarkers]
                        }
                    },
                    { name: '高点趋势线', type: 'line', data: highTrends, smooth: false, symbol: 'none', lineStyle: { color: '#1E90FF', width: 2 } },
                    { name: '低点趋势线', type: 'line', data: lowTrends, smooth: false, symbol: 'none', lineStyle: { color: '#FF8C00', width: 2 } },
                    {
                        name: '买入信号',
                        type: 'scatter',
                        data: buySignals.map(b => [stockData[b.dayIndex].date, b.price]),
                        symbol: 'triangle',
                        symbolSize: 20,
                        itemStyle: { color: '#00FF00', borderColor: '#008000', borderWidth: 2 },
                        label: { show: false },
                        emphasis: {
                            label: {
                                show: true,
                                formatter: '买入: {c}',
                                backgroundColor: '#00AA00',
                                color: '#FFFFFF',
                                padding: [4, 8],
                                borderRadius: 4
                            }
                        }
                    },
                    {
                        name: '卖出信号',
                        type: 'scatter',
                        data: sellSignals.map(s => [stockData[s.dayIndex].date, s.price]),
                        symbol: 'triangle',
                        symbolSize: 20,
                        symbolRotate: 180,
                        itemStyle: { color: '#FF0000', borderColor: '#800000', borderWidth: 2 },
                        label: { show: false },
                        emphasis: {
                            label: {
                                show: true,
                                formatter: '卖出: {c}',
                                backgroundColor: '#CC0000',
                                color: '#FFFFFF',
                                padding: [4, 8],
                                borderRadius: 4
                            }
                        }
                    }
                ]
            };
            chart.setOption(option);
        }

        // 处理交易信号并更新交易记录 - 只处理当前日期的信号
        function processTradeSignals(currentRecord) {
            const currentDate = currentRecord.date; // 当前处理的日期
            const buySignals = calculator.getBuySignals();
            const sellSignals = calculator.getSellSignals();

            console.log(`🔍 处理交易信号 - 当前日期: ${currentDate}`);
            console.log(`📊 总买入信号: ${buySignals.length}个`, buySignals);
            console.log(`📊 总卖出信号: ${sellSignals.length}个`, sellSignals);

            // 只处理当前日期的买入信号
            const todayBuySignals = buySignals.filter(signal =>
                signal.date === currentDate
            );

            console.log(`🟢 当前日期买入信号: ${todayBuySignals.length}个`, todayBuySignals);

            todayBuySignals.forEach(signal => {
                console.log(`🔍 处理买入信号: ${signal.date} @ ${signal.price}`);

                if (!tradingRecords.find(r => r.date === currentDate && r.type === 'BUY')) {
                    console.log(`✅ 没有重复的买入记录，继续处理`);

                    if (!currentPosition) {
                        console.log(`✅ 当前无持仓，可以买入`);

                        // 执行买入
                        const quantity = Math.floor(cash / signal.price);
                        const amount = quantity * signal.price;

                        console.log(`💰 买入计算: 现金=${cash}, 价格=${signal.price}, 数量=${quantity}, 金额=${amount}`);

                        if (quantity > 0) {
                            tradePairId++; // 新的交易对

                            currentPosition = {
                                type: 'LONG',
                                quantity: quantity,
                                entryPrice: signal.price,
                                entryDate: currentDate,
                                entryAmount: amount,
                                pairId: tradePairId
                            };

                            cash -= amount;
                            tradeSequence++;

                            const buyRecord = {
                                sequence: tradeSequence,
                                pairId: tradePairId,
                                date: currentDate,
                                type: 'BUY',
                                price: signal.price,
                                quantity: quantity,
                                amount: amount,
                                profit: 0,
                                profitRate: 0,
                                reason: signal.reason || '买入信号',
                                pairInfo: null,
                                status: 'OPEN'
                            };

                            tradingRecords.push(buyRecord);

                            console.log(`🎉 买入成功！`, buyRecord);
                            console.log(`💰 剩余现金: ${cash}, 交易记录数: ${tradingRecords.length}`);
                        } else {
                            console.log(`❌ 买入失败：数量为0`);
                        }
                    } else {
                        console.log(`❌ 已有持仓，无法买入`);
                    }
                } else {
                    console.log(`❌ 已有买入记录，跳过`);
                }
                    }
                }
            });

            // 只处理当前日期的卖出信号
            const todaySellSignals = sellSignals.filter(signal =>
                signal.date === currentDate
            );

            console.log(`🔴 当前日期卖出信号: ${todaySellSignals.length}个`, todaySellSignals);

            todaySellSignals.forEach(signal => {
                if (!tradingRecords.find(r => r.date === currentDate && r.type === 'SELL')) {
                    if (currentPosition) {
                        // 执行卖出
                        const sellAmount = currentPosition.quantity * signal.price;
                        const profit = sellAmount - currentPosition.entryAmount;
                        const profitRate = (profit / currentPosition.entryAmount) * 100;

                        cash += sellAmount;
                        tradeSequence++;

                        // 找到对应的买入记录并更新状态
                        const buyRecord = tradingRecords.find(r => r.pairId === currentPosition.pairId && r.type === 'BUY');
                        if (buyRecord) {
                            buyRecord.status = 'CLOSED';
                            buyRecord.pairInfo = {
                                sellDate: currentDate,
                                sellPrice: signal.price,
                                profit: profit,
                                profitRate: profitRate
                            };
                            // 更新买入记录的盈亏信息
                            buyRecord.profit = profit;
                            buyRecord.profitRate = profitRate;
                        }

                        tradingRecords.push({
                            sequence: tradeSequence,
                            pairId: currentPosition.pairId,
                            date: currentDate,
                            type: 'SELL',
                            price: signal.price,
                            quantity: currentPosition.quantity,
                            amount: sellAmount,
                            profit: profit,
                            profitRate: profitRate,
                            reason: signal.reason || '卖出信号',
                            pairInfo: {
                                buyDate: currentPosition.entryDate,
                                buyPrice: currentPosition.entryPrice,
                                holdDays: calculateHoldDays(currentPosition.entryDate, currentDate)
                            },
                            status: 'CLOSED'
                        });

                        currentPosition = null;
                    }
                }
            });
        }

        // 计算持有天数
        function calculateHoldDays(buyDate, sellDate) {
            const buy = new Date(buyDate);
            const sell = new Date(sellDate);
            const diffTime = Math.abs(sell - buy);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            return diffDays;
        }

        // 更新交易明细表格
        function updateTradingTable() {
            const tableBody = document.getElementById('tradingTableBody');

            if (tradingRecords.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="12" class="no-trades">暂无交易记录，开始添加数据后将显示交易信号</td></tr>';
                return;
            }

            let html = '';
            tradingRecords.forEach(record => {
                const typeClass = record.type === 'BUY' ? 'buy-signal' : 'sell-signal';
                const profitClass = record.profit > 0 ? 'profit-positive' : (record.profit < 0 ? 'profit-negative' : '');

                // 构建配对信息
                let pairInfo = '--';
                let holdDays = '--';
                let status = record.status === 'OPEN' ? '🟡 持仓中' : '✅ 已平仓';

                if (record.type === 'BUY' && record.pairInfo) {
                    // 买入记录显示对应的卖出信息
                    pairInfo = `卖出: ${record.pairInfo.sellDate} @ ${record.pairInfo.sellPrice.toFixed(2)}`;
                    holdDays = calculateHoldDays(record.date, record.pairInfo.sellDate);
                } else if (record.type === 'SELL' && record.pairInfo) {
                    // 卖出记录显示对应的买入信息
                    pairInfo = `买入: ${record.pairInfo.buyDate} @ ${record.pairInfo.buyPrice.toFixed(2)}`;
                    holdDays = record.pairInfo.holdDays;
                }

                html += `
                    <tr class="${record.status === 'OPEN' ? 'open-position' : 'closed-position'}">
                        <td><span class="pair-badge">P${record.pairId}</span></td>
                        <td>${record.sequence}</td>
                        <td>${record.date}</td>
                        <td class="${typeClass}">${record.type === 'BUY' ? '🟢 买入' : '🔴 卖出'}</td>
                        <td>${record.price.toFixed(2)}</td>
                        <td>${record.quantity}</td>
                        <td>${record.amount.toFixed(2)}</td>
                        <td class="pair-info">${pairInfo}</td>
                        <td class="${profitClass}">${record.profit === 0 ? '--' : (record.profit > 0 ? '+' : '') + record.profit.toFixed(2)}</td>
                        <td class="${profitClass}">${record.profitRate === 0 ? '--' : (record.profitRate > 0 ? '+' : '') + record.profitRate.toFixed(2) + '%'}</td>
                        <td>${holdDays}</td>
                        <td>${status}</td>
                    </tr>
                `;
            });

            tableBody.innerHTML = html;

            // 滚动到最新记录
            const tableContainer = document.querySelector('.table-container');
            tableContainer.scrollTop = tableContainer.scrollHeight;
        }

        function addNextDay() {
            if (currentIndex >= stockData.length) {
                document.getElementById('info').textContent = '所有数据已添加完毕！';
                document.getElementById('nextBtn').disabled = true;
                return;
            }
            const record = stockData[currentIndex];
            calculator.addRecord(record.date, record.high, record.low, record.close);
            currentIndex++;
            updateChart();

            // 处理交易信号并更新交易表格
            processTradeSignals(record);
            updateTradingTable();

            const peaks = calculator.turnPointDetector.getPeaks();
            const troughs = calculator.turnPointDetector.getTroughs();
            const buySignals = calculator.getBuySignals();
            const sellSignals = calculator.getSellSignals();

            // 更新信息显示，包含交易统计
            let infoText = `已添加第 ${currentIndex} 个交易日: ${record.date} | 波峰: ${peaks.length}个, 波谷: ${troughs.length}个 | 买入信号: ${buySignals.length}个, 卖出信号: ${sellSignals.length}个`;

            if (tradingRecords.length > 0) {
                const completedTrades = tradingRecords.filter(r => r.type === 'SELL').length;
                const totalProfit = tradingRecords.filter(r => r.type === 'SELL').reduce((sum, r) => sum + r.profit, 0);
                infoText += ` | 交易记录: ${tradingRecords.length}条, 完成交易: ${completedTrades}笔`;
                if (completedTrades > 0) {
                    infoText += `, 总盈亏: ${totalProfit > 0 ? '+' : ''}${totalProfit.toFixed(2)}`;
                }
            }

            if (currentPosition) {
                const currentValue = currentPosition.quantity * record.close;
                const unrealizedProfit = currentValue - currentPosition.entryAmount;
                infoText += ` | 当前持仓: ${currentPosition.quantity}股@${currentPosition.entryPrice.toFixed(2)}, 浮盈: ${unrealizedProfit > 0 ? '+' : ''}${unrealizedProfit.toFixed(2)}`;
            }

            document.getElementById('info').textContent = infoText;

            if (currentIndex >= stockData.length) {
                document.getElementById('nextBtn').disabled = true;
                document.getElementById('info').textContent += ' - 所有数据已添加完毕！';
            }
        }

        function resetChart() {
            currentIndex = 0;
            calculator = new StockTrendCalculator(windowSize, turnPointThreshold);
            chart.clear();

            // 重置交易记录
            tradingRecords = [];
            currentPosition = null;
            cash = 100000;
            tradeSequence = 0;
            tradePairId = 0;
            updateTradingTable();

            document.getElementById('nextBtn').disabled = false;
            document.getElementById('currentDay').textContent = '0';
            document.getElementById('highSlope').textContent = '--';
            document.getElementById('lowSlope').textContent = '--';
            document.getElementById('info').textContent = '图表已重置，点击"添加下一个交易日"开始分析';
            stopAuto();
        }

        function autoPlay() {
            if (autoPlayInterval) return;
            document.getElementById('autoBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            autoPlayInterval = setInterval(() => {
                if (currentIndex >= stockData.length) {
                    stopAuto();
                    return;
                }
                addNextDay();
            }, 200); // 播放速度加快
        }

        function stopAuto() {
            if (autoPlayInterval) {
                clearInterval(autoPlayInterval);
                autoPlayInterval = null;
            }
            document.getElementById('autoBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
        }

        updateChart();
    </script>
</body>
</html>
    