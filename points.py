import numpy as np
import akshare as ak
from dataclasses import dataclass
import json
import webbrowser
import os

@dataclass
class TurnPoint:
    """转折点数据结构"""
    date: str
    price: float
    point_type: str  # 'PEAK' or 'TROUGH'
    day_index: int

@dataclass
class TradeSignal:
    """交易信号数据结构"""
    date: str
    price: float
    signal_type: str  # 'BUY' or 'SELL'
    day_index: int
    reason: str  # 信号原因
    confidence: float  # 信号置信度 (0-1)

class EnhancedTurnPointDetector:
    """
    增强的转折点检测器 (基于Zigzag思想)
    通过价格反转百分比阈值来确定重要的波峰和波谷。
    """
    def __init__(self, percent_change_threshold: float = 10.0):
        self.threshold = percent_change_threshold / 100.0
        self.records = []
        self.turn_points = []
        
        self.last_extreme_price = None
        self.last_extreme_point_info = None # (date, price, day_index)
        self.current_trend = 0  # 0: undefined, 1: up, -1: down

        # 用于处理初始趋势的临时变量
        self.temp_first_point_high_info = None
        self.temp_first_point_low_info = None

    def add_record(self, date: str, high: float, low: float, day_index: int):
        record = {'date': date, 'high': high, 'low': low, 'day_index': day_index}
        self.records.append(record)
        
        if self.current_trend == 0:
            if not self.temp_first_point_high_info:
                # 记录第一个点的高点和低点信息
                self.temp_first_point_high_info = (date, high, day_index)
                self.temp_first_point_low_info = (date, low, day_index)
            else:
                # 从第一个点开始寻找足够大的价格波动来确定初始趋势
                first_high = self.temp_first_point_high_info[1]
                
                if high > first_high * (1 + self.threshold):
                    # 价格显著上涨，初始点为波谷
                    self.current_trend = 1  # UP
                    trough = TurnPoint(date=self.temp_first_point_low_info[0], price=self.temp_first_point_low_info[1], point_type='TROUGH', day_index=self.temp_first_point_low_info[2])
                    self.turn_points.append(trough)
                    self.last_extreme_price = high
                    self.last_extreme_point_info = (date, high, day_index)
                elif low < first_high * (1 - self.threshold):
                    # 价格显著下跌，初始点为波峰
                    self.current_trend = -1  # DOWN
                    peak = TurnPoint(date=self.temp_first_point_high_info[0], price=self.temp_first_point_high_info[1], point_type='PEAK', day_index=self.temp_first_point_high_info[2])
                    self.turn_points.append(peak)
                    self.last_extreme_price = low
                    self.last_extreme_point_info = (date, low, day_index)
                else:
                    # 如果没有足够波动，更新潜在的初始高低点
                    if high > self.temp_first_point_high_info[1]:
                        self.temp_first_point_high_info = (date, high, day_index)
                    if low < self.temp_first_point_low_info[1]:
                        self.temp_first_point_low_info = (date, low, day_index)

        elif self.current_trend == 1:  # 趋势向上，寻找波峰
            if high > self.last_extreme_price:
                # 发现新高，更新潜在波峰
                self.last_extreme_price = high
                self.last_extreme_point_info = (date, high, day_index)
            elif low < self.last_extreme_price * (1 - self.threshold):
                # 价格从高点回落超过阈值，确认波峰
                peak = TurnPoint(date=self.last_extreme_point_info[0], price=self.last_extreme_point_info[1], point_type='PEAK', day_index=self.last_extreme_point_info[2])
                # 放宽条件：只要是有效的波峰就添加
                self.turn_points.append(peak)
                self.current_trend = -1  # 趋势转为向下
                self.last_extreme_price = low
                self.last_extreme_point_info = (date, low, day_index)

        elif self.current_trend == -1:  # 趋势向下，寻找波谷
            if low < self.last_extreme_price:
                # 发现新低，更新潜在波谷
                self.last_extreme_price = low
                self.last_extreme_point_info = (date, low, day_index)
            elif high > self.last_extreme_price * (1 + self.threshold):
                # 价格从低点反弹超过阈值，确认波谷
                trough = TurnPoint(date=self.last_extreme_point_info[0], price=self.last_extreme_point_info[1], point_type='TROUGH', day_index=self.last_extreme_point_info[2])
                # 放宽条件：只要是有效的波谷就添加
                self.turn_points.append(trough)
                self.current_trend = 1  # 趋势转为向上
                self.last_extreme_price = high
                self.last_extreme_point_info = (date, high, day_index)

    def get_peaks(self):
        return [p for p in self.turn_points if p.point_type == 'PEAK']

    def get_troughs(self):
        return [t for t in self.turn_points if t.point_type == 'TROUGH']

    def get_turn_points_info(self):
        return self.get_peaks(), self.get_troughs()

class AdvancedTurnPointDetector:
    """
    高级转折点检测器 - 专门用于捕捉上升趋势中的低点和震荡
    """
    def __init__(self, percent_change_threshold: float = 10.0, min_swing_threshold: float = 5.0):
        self.threshold = percent_change_threshold / 100.0
        self.min_swing = min_swing_threshold / 100.0  # 最小震荡幅度
        self.records = []
        self.turn_points = []
        self.potential_peaks = []  # 潜在波峰列表
        self.potential_troughs = []  # 潜在波谷列表

    def add_record(self, date: str, high: float, low: float, close: float, day_index: int):
        record = {'date': date, 'high': high, 'low': low, 'close': close, 'day_index': day_index}
        self.records.append(record)

        # 检测潜在的局部极值点
        self._detect_local_extremes(record)

        # 确认转折点
        self._confirm_turn_points()

    def _detect_local_extremes(self, current_record):
        """检测局部极值点"""
        if len(self.records) < 3:
            return

        # 获取最近3个记录进行局部极值检测
        recent = self.records[-3:]
        prev_record = recent[0]
        mid_record = recent[1]
        curr_record = current_record

        # 检测局部高点
        if (mid_record['high'] >= prev_record['high'] and
            mid_record['high'] >= curr_record['high']):
            potential_peak = TurnPoint(
                date=mid_record['date'],
                price=mid_record['high'],
                point_type='PEAK',
                day_index=mid_record['day_index']
            )
            self.potential_peaks.append(potential_peak)

        # 检测局部低点
        if (mid_record['low'] <= prev_record['low'] and
            mid_record['low'] <= curr_record['low']):
            potential_trough = TurnPoint(
                date=mid_record['date'],
                price=mid_record['low'],
                point_type='TROUGH',
                day_index=mid_record['day_index']
            )
            self.potential_troughs.append(potential_trough)

    def _confirm_turn_points(self):
        """确认转折点"""
        # 确认波峰
        confirmed_peaks = []
        for peak in self.potential_peaks:
            if self._is_significant_peak(peak):
                confirmed_peaks.append(peak)

        # 确认波谷
        confirmed_troughs = []
        for trough in self.potential_troughs:
            if self._is_significant_trough(trough):
                confirmed_troughs.append(trough)

        # 合并并排序转折点
        all_points = confirmed_peaks + confirmed_troughs
        all_points.sort(key=lambda x: x.day_index)

        # 过滤重复和无效的转折点
        self.turn_points = self._filter_turn_points(all_points)

    def _is_significant_peak(self, peak):
        """判断是否为显著波峰"""
        # 检查是否有足够的回落
        for record in self.records:
            if (record['day_index'] > peak.day_index and
                record['low'] < peak.price * (1 - self.threshold)):
                return True
        return False

    def _is_significant_trough(self, trough):
        """判断是否为显著波谷"""
        # 检查是否有足够的反弹
        for record in self.records:
            if (record['day_index'] > trough.day_index and
                record['high'] > trough.price * (1 + self.threshold)):
                return True
        return False

    def _filter_turn_points(self, points):
        """过滤转折点，确保交替出现且满足最小震荡要求"""
        if len(points) < 2:
            return points

        filtered = [points[0]]

        for point in points[1:]:
            last_point = filtered[-1]

            # 确保转折点类型交替
            if point.point_type != last_point.point_type:
                # 检查震荡幅度是否足够
                if point.point_type == 'PEAK':
                    swing_ratio = (point.price - last_point.price) / last_point.price
                else:
                    swing_ratio = (last_point.price - point.price) / last_point.price

                if swing_ratio >= self.min_swing:
                    filtered.append(point)

        return filtered

    def get_peaks(self):
        return [p for p in self.turn_points if p.point_type == 'PEAK']

    def get_troughs(self):
        return [t for t in self.turn_points if t.point_type == 'TROUGH']

    def get_uptrend_troughs(self):
        """获取上升趋势中的波谷"""
        troughs = self.get_troughs()
        if len(troughs) < 2:
            return troughs

        uptrend_troughs = []
        for i in range(1, len(troughs)):
            # 如果当前波谷高于前一个波谷，认为是上升趋势中的波谷
            if troughs[i].price > troughs[i-1].price:
                uptrend_troughs.append(troughs[i])

        return uptrend_troughs

class SmartTradingStrategy:
    """
    智能交易策略 - 基于转折点和趋势分析的买卖策略
    重构为逐日分析模式，支持实时信号生成和头寸管理
    """
    def __init__(self,
                 buy_threshold=10.0,      # 买入阈值(%)
                 sell_threshold=15.0,     # 卖出阈值(%)
                 stop_loss=8.0,          # 止损阈值(%)
                 trend_confirmation=3):   # 趋势确认天数
        self.buy_threshold = buy_threshold / 100.0
        self.sell_threshold = sell_threshold / 100.0
        self.stop_loss = stop_loss / 100.0
        self.trend_confirmation = trend_confirmation

        # 信号和交易记录
        self.signals = []
        self.daily_signals = []  # 每日生成的信号
        self.trade_history = []

        # 头寸管理
        self.position = None  # 当前持仓信息 {'type': 'LONG', 'entry_price': float, 'entry_date': str, 'quantity': int}
        self.cash = 100000.0  # 初始资金
        self.portfolio_value = 100000.0  # 组合价值

        # 状态跟踪
        self.last_processed_day = -1  # 最后处理的交易日
        self.processed_turn_points = set()  # 已处理的转折点ID

    def process_daily_data(self, calculator, current_record):
        """
        逐日处理数据，生成交易信号和管理头寸

        Args:
            calculator: StockTrendCalculator实例
            current_record: 当前日的数据记录

        Returns:
            list: 当日生成的信号列表
        """
        current_day = current_record['day_index']

        # 避免重复处理同一天的数据
        if current_day <= self.last_processed_day:
            return []

        self.last_processed_day = current_day
        daily_signals = []

        # 1. 检查止损止盈
        stop_signals = self._check_stop_conditions(current_record)
        daily_signals.extend(stop_signals)

        # 2. 检查新的转折点信号
        turn_point_signals = self._check_turn_point_signals(calculator, current_record)
        daily_signals.extend(turn_point_signals)

        # 3. 更新组合价值
        self._update_portfolio_value(current_record)

        # 4. 记录当日信号
        if daily_signals:
            self.daily_signals.extend(daily_signals)
            self.signals.extend(daily_signals)

        return daily_signals

    def analyze_signals(self, calculator):
        """
        兼容性方法：基于转折点分析生成交易信号（保留原有接口）
        """
        peaks = calculator.get_turn_points_info()[0]
        troughs = calculator.get_turn_points_info()[1]
        uptrend_troughs = calculator.get_uptrend_troughs()

        # 生成买入信号
        self._generate_buy_signals(troughs, uptrend_troughs, calculator)

        # 生成卖出信号
        self._generate_sell_signals(peaks, calculator)

        return self.signals

    def _check_stop_conditions(self, current_record):
        """
        检查止损止盈条件

        Args:
            current_record: 当前日的数据记录

        Returns:
            list: 止损止盈信号列表
        """
        signals = []

        if self.position is None:
            return signals

        current_price = current_record['close']
        entry_price = self.position['entry_price']

        if self.position['type'] == 'LONG':
            # 止损检查
            if current_price <= entry_price * (1 - self.stop_loss):
                signal = TradeSignal(
                    date=current_record['date'],
                    price=current_price,
                    signal_type='SELL',
                    day_index=current_record['day_index'],
                    reason=f'止损 (入场: {entry_price:.2f}, 当前: {current_price:.2f})',
                    confidence=1.0
                )
                signals.append(signal)
                self._execute_trade(signal, current_record)

        return signals

    def _check_turn_point_signals(self, calculator, current_record):
        """
        检查转折点相关的交易信号

        Args:
            calculator: StockTrendCalculator实例
            current_record: 当前日的数据记录

        Returns:
            list: 转折点信号列表
        """
        signals = []

        # 获取最新的转折点
        peaks = calculator.get_turn_points_info()[0]
        troughs = calculator.get_turn_points_info()[1]

        # 检查是否有新的转折点
        for trough in troughs:
            trough_id = f"trough_{trough.day_index}_{trough.price}"
            if trough_id not in self.processed_turn_points:
                self.processed_turn_points.add(trough_id)

                # 检查买入机会
                if self.position is None:  # 只在无持仓时考虑买入
                    buy_signal = self._evaluate_buy_opportunity(trough, calculator, current_record)
                    if buy_signal:
                        signals.append(buy_signal)
                        self._execute_trade(buy_signal, current_record)

        for peak in peaks:
            peak_id = f"peak_{peak.day_index}_{peak.price}"
            if peak_id not in self.processed_turn_points:
                self.processed_turn_points.add(peak_id)

                # 检查卖出机会
                if self.position is not None and self.position['type'] == 'LONG':
                    sell_signal = self._evaluate_sell_opportunity(peak, calculator, current_record)
                    if sell_signal:
                        signals.append(sell_signal)
                        self._execute_trade(sell_signal, current_record)

        return signals

    def _evaluate_buy_opportunity(self, trough, calculator, current_record):
        """
        评估买入机会

        Args:
            trough: 波谷转折点
            calculator: StockTrendCalculator实例
            current_record: 当前日的数据记录

        Returns:
            TradeSignal or None: 买入信号或None
        """
        # 检查是否为上升趋势中的波谷
        uptrend_troughs = calculator.get_uptrend_troughs()
        is_uptrend_trough = any(ut.day_index == trough.day_index for ut in uptrend_troughs)

        if is_uptrend_trough:
            # 检查反弹确认（简化版：检查当前价格是否已经反弹）
            current_price = current_record['close']
            if current_price > trough.price * (1 + self.buy_threshold * 0.5):  # 部分反弹确认
                return TradeSignal(
                    date=current_record['date'],
                    price=current_price,
                    signal_type='BUY',
                    day_index=current_record['day_index'],
                    reason='上升趋势波谷回调买入',
                    confidence=0.8
                )

        # 检查超跌买入机会
        if self._is_oversold_opportunity(trough, calculator):
            current_price = current_record['close']
            return TradeSignal(
                date=current_record['date'],
                price=current_price,
                signal_type='BUY',
                day_index=current_record['day_index'],
                reason='超跌反弹买入',
                confidence=0.9
            )

        return None

    def _evaluate_sell_opportunity(self, peak, calculator, current_record):
        """
        评估卖出机会

        Args:
            peak: 波峰转折点
            calculator: StockTrendCalculator实例
            current_record: 当前日的数据记录

        Returns:
            TradeSignal or None: 卖出信号或None
        """
        if self.position is None:
            return None

        entry_price = self.position['entry_price']
        current_price = current_record['close']

        # 计算当前收益率
        gain_ratio = (current_price - entry_price) / entry_price

        # 如果收益率达到卖出阈值
        if gain_ratio >= self.sell_threshold:
            return TradeSignal(
                date=current_record['date'],
                price=current_price,
                signal_type='SELL',
                day_index=current_record['day_index'],
                reason=f'高位获利了结 (收益率: {gain_ratio*100:.1f}%)',
                confidence=0.8
            )

        return None

    def _execute_trade(self, signal, current_record):
        """
        执行交易

        Args:
            signal: 交易信号
            current_record: 当前日的数据记录
        """
        if signal.signal_type == 'BUY':
            if self.position is None:
                # 计算买入数量（使用80%的可用资金）
                available_cash = self.cash * 0.8
                quantity = int(available_cash / signal.price)

                if quantity > 0:
                    cost = quantity * signal.price
                    self.cash -= cost
                    self.position = {
                        'type': 'LONG',
                        'entry_price': signal.price,
                        'entry_date': signal.date,
                        'quantity': quantity,
                        'cost': cost
                    }

                    # 记录交易
                    self.trade_history.append({
                        'type': 'BUY',
                        'date': signal.date,
                        'price': signal.price,
                        'quantity': quantity,
                        'amount': cost,
                        'reason': signal.reason
                    })

        elif signal.signal_type == 'SELL':
            if self.position is not None and self.position['type'] == 'LONG':
                quantity = self.position['quantity']
                proceeds = quantity * signal.price
                self.cash += proceeds

                # 计算盈亏
                cost = self.position['cost']
                profit = proceeds - cost
                profit_ratio = profit / cost

                # 记录交易
                self.trade_history.append({
                    'type': 'SELL',
                    'date': signal.date,
                    'price': signal.price,
                    'quantity': quantity,
                    'amount': proceeds,
                    'profit': profit,
                    'profit_ratio': profit_ratio,
                    'reason': signal.reason
                })

                # 清空持仓
                self.position = None

    def _update_portfolio_value(self, current_record):
        """
        更新组合价值

        Args:
            current_record: 当前日的数据记录
        """
        if self.position is not None:
            current_price = current_record['close']
            position_value = self.position['quantity'] * current_price
            self.portfolio_value = self.cash + position_value
        else:
            self.portfolio_value = self.cash

    def _is_oversold_opportunity(self, trough, calculator):
        """
        判断是否为超跌买入机会

        Args:
            trough: 波谷转折点
            calculator: StockTrendCalculator实例

        Returns:
            bool: 是否为超跌买入机会
        """
        # 寻找前一个波峰
        peaks = calculator.get_turn_points_info()[0]
        prev_peak = None

        for peak in reversed(peaks):
            if peak.day_index < trough.day_index:
                prev_peak = peak
                break

        if prev_peak is None:
            return False

        # 计算跌幅
        decline_ratio = (prev_peak.price - trough.price) / prev_peak.price

        # 如果跌幅超过买入阈值的1.5倍，认为是超跌
        return decline_ratio >= self.buy_threshold * 1.5

    def get_current_position(self):
        """获取当前持仓信息"""
        return self.position

    def get_portfolio_value(self):
        """获取当前组合价值"""
        return self.portfolio_value

    def get_trade_history(self):
        """获取交易历史"""
        return self.trade_history

    def get_daily_signals(self):
        """获取每日信号记录"""
        return self.daily_signals

    def reset_strategy(self):
        """重置策略状态"""
        self.signals = []
        self.daily_signals = []
        self.trade_history = []
        self.position = None
        self.cash = 100000.0
        self.portfolio_value = 100000.0
        self.last_processed_day = -1
        self.processed_turn_points = set()

    def _generate_buy_signals(self, troughs, uptrend_troughs, calculator):
        """生成买入信号"""
        for trough in uptrend_troughs:
            # 策略1: 上升趋势中的波谷 + 反弹确认
            if self._is_uptrend_buy_opportunity(trough, calculator):
                signal = TradeSignal(
                    date=trough.date,
                    price=trough.price,
                    signal_type='BUY',
                    day_index=trough.day_index,
                    reason='上升趋势波谷回调买入',
                    confidence=0.8
                )
                self.signals.append(signal)

        # 策略2: 大幅下跌后的反弹买入
        for trough in troughs:
            if self._is_oversold_buy_opportunity(trough, calculator):
                signal = TradeSignal(
                    date=trough.date,
                    price=trough.price,
                    signal_type='BUY',
                    day_index=trough.day_index,
                    reason='超跌反弹买入',
                    confidence=0.9
                )
                self.signals.append(signal)

    def _generate_sell_signals(self, peaks, calculator):
        """生成卖出信号"""
        for peak in peaks:
            # 策略1: 高位获利了结
            if self._is_profit_taking_opportunity(peak, calculator):
                signal = TradeSignal(
                    date=peak.date,
                    price=peak.price,
                    signal_type='SELL',
                    day_index=peak.day_index,
                    reason='高位获利了结',
                    confidence=0.8
                )
                self.signals.append(signal)

    def _is_uptrend_buy_opportunity(self, trough, calculator):
        """判断是否为上升趋势买入机会"""
        # 检查是否有足够的反弹确认
        records = calculator.records
        trough_idx = next((i for i, r in enumerate(records) if r['date'] == trough.date), -1)

        if trough_idx == -1 or trough_idx + self.trend_confirmation >= len(records):
            return False

        # 检查后续几天是否有反弹
        for i in range(1, min(self.trend_confirmation + 1, len(records) - trough_idx)):
            future_record = records[trough_idx + i]
            if future_record['high'] > trough.price * (1 + self.buy_threshold):
                return True
        return False

    def _is_oversold_buy_opportunity(self, trough, calculator):
        """判断是否为超跌买入机会"""
        # 寻找前一个波峰
        peaks = calculator.get_turn_points_info()[0]
        prev_peak = None

        for peak in reversed(peaks):
            if peak.day_index < trough.day_index:
                prev_peak = peak
                break

        if prev_peak is None:
            return False

        # 计算跌幅
        decline_ratio = (prev_peak.price - trough.price) / prev_peak.price

        # 如果跌幅超过阈值，且有反弹确认，则为买入机会
        if decline_ratio >= self.buy_threshold:
            return self._is_uptrend_buy_opportunity(trough, calculator)

        return False

    def _is_profit_taking_opportunity(self, peak, calculator):
        """判断是否为获利了结机会"""
        # 寻找前一个波谷
        troughs = calculator.get_turn_points_info()[1]
        prev_trough = None

        for trough in reversed(troughs):
            if trough.day_index < peak.day_index:
                prev_trough = trough
                break

        if prev_trough is None:
            return False

        # 计算涨幅
        gain_ratio = (peak.price - prev_trough.price) / prev_trough.price

        # 如果涨幅超过阈值，则为卖出机会
        return gain_ratio >= self.sell_threshold

    def get_signals_by_type(self, signal_type):
        """获取指定类型的信号"""
        return [s for s in self.signals if s.signal_type == signal_type]

    def calculate_performance(self):
        """
        计算策略表现 - 基于实际交易历史
        """
        if not self.trade_history:
            return {
                'total_trades': 0,
                'win_rate': 0,
                'total_return': 0,
                'avg_return': 0,
                'max_drawdown': 0,
                'portfolio_value': self.portfolio_value,
                'cash': self.cash,
                'trades': []
            }

        # 统计完成的交易对
        completed_trades = []
        buy_trades = [t for t in self.trade_history if t['type'] == 'BUY']
        sell_trades = [t for t in self.trade_history if t['type'] == 'SELL']

        # 匹配买卖交易对
        for sell_trade in sell_trades:
            if 'profit_ratio' in sell_trade:
                completed_trades.append(sell_trade)

        if not completed_trades:
            return {
                'total_trades': 0,
                'win_rate': 0,
                'total_return': 0,
                'avg_return': 0,
                'max_drawdown': 0,
                'portfolio_value': self.portfolio_value,
                'cash': self.cash,
                'trades': []
            }

        # 计算统计指标
        win_trades = [t for t in completed_trades if t['profit_ratio'] > 0]
        total_return = sum(t['profit_ratio'] for t in completed_trades)

        # 计算最大回撤（简化版）
        portfolio_values = [100000.0]  # 初始值
        running_value = 100000.0
        for trade in completed_trades:
            running_value *= (1 + trade['profit_ratio'])
            portfolio_values.append(running_value)

        max_drawdown = 0
        peak = portfolio_values[0]
        for value in portfolio_values:
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak
            if drawdown > max_drawdown:
                max_drawdown = drawdown

        return {
            'total_trades': len(completed_trades),
            'win_rate': len(win_trades) / len(completed_trades) * 100 if completed_trades else 0,
            'total_return': total_return * 100,
            'avg_return': total_return / len(completed_trades) * 100 if completed_trades else 0,
            'max_drawdown': max_drawdown * 100,
            'portfolio_value': self.portfolio_value,
            'cash': self.cash,
            'current_position': self.position,
            'trades': completed_trades
        }

class StockTrendCalculator:
    def __init__(self, window_size=60, turn_point_threshold=10.0, use_advanced_detector=True, enable_trading=True):
        """
        初始化股票趋势线计算器
        :param window_size: 时间窗口大小
        :param turn_point_threshold: 转折点检测的百分比阈值
        :param use_advanced_detector: 是否使用高级检测器
        :param enable_trading: 是否启用交易策略
        """
        self.window_size = window_size
        self.records = []
        self.day_counter = 0
        self.use_advanced = use_advanced_detector

        if use_advanced_detector:
            self.turn_point_detector = AdvancedTurnPointDetector(
                percent_change_threshold=turn_point_threshold,
                min_swing_threshold=5.0  # 最小5%震荡
            )
        else:
            self.turn_point_detector = EnhancedTurnPointDetector(percent_change_threshold=turn_point_threshold)

        # 交易策略
        self.trading_strategy = SmartTradingStrategy(
            buy_threshold=8.0,   # 8%回调买入
            sell_threshold=15.0, # 15%涨幅卖出
            stop_loss=8.0,      # 8%止损
            trend_confirmation=2 # 2天确认
        ) if enable_trading else None

    def add_record(self, timestamp, high, low, close=None):
        """
        添加新的股票记录，逐条处理
        """
        if close is None:
            close = (high + low) / 2  # 如果没有收盘价，使用高低价平均值

        record = {
            'date': str(timestamp),
            'high': high,
            'low': low,
            'close': close,
            'day_index': self.day_counter
        }
        self.records.append(record)

        if self.use_advanced:
            self.turn_point_detector.add_record(str(timestamp), high, low, close, self.day_counter)
        else:
            self.turn_point_detector.add_record(str(timestamp), high, low, self.day_counter)

        self.day_counter += 1
        if len(self.records) > self.window_size:
            self.records.pop(0)

        # 逐日处理交易策略
        if self.trading_strategy is not None:
            daily_signals = self.trading_strategy.process_daily_data(self, record)
            if daily_signals:
                # 可以在这里添加日志记录或其他处理
                pass

    def get_current_trend_lines(self):
        """
        获取当前的高点和低点趋势线参数
        """
        peaks = self.turn_point_detector.get_peaks()
        troughs = self.turn_point_detector.get_troughs()
        current_start_day = max(0, self.day_counter - self.window_size)
        recent_peaks = [p for p in peaks if p.day_index >= current_start_day]
        recent_troughs = [t for t in troughs if t.day_index >= current_start_day]

        high_line = None
        if len(recent_peaks) >= 2:
            peak_points = [(p.day_index, p.price) for p in recent_peaks]
            high_line = self._fit_weighted_line(peak_points)

        low_line = None
        if len(recent_troughs) >= 2:
            trough_points = [(t.day_index, t.price) for t in recent_troughs]
            low_line = self._fit_weighted_line(trough_points)

        return high_line, low_line

    def _fit_weighted_line(self, points):
        """
        使用加权最小二乘法拟合直线，给近期点更高权重
        """
        if len(points) < 2:
            return None
        n = len(points)
        x_values = [p[0] for p in points]
        y_values = [p[1] for p in points]
        decay_factor = 0.05
        weights = [np.exp(-(n - 1 - i) * decay_factor) for i in range(n)]
        sum_w = sum(weights)
        sum_wx = sum(w * x for w, x in zip(weights, x_values))
        sum_wy = sum(w * y for w, y in zip(weights, y_values))
        sum_wxx = sum(w * x * x for w, x in zip(weights, x_values))
        sum_wxy = sum(w * x * y for w, x, y in zip(weights, x_values, y_values))
        denominator = sum_w * sum_wxx - sum_wx * sum_wx
        if abs(denominator) < 1e-10:
            slope = 0
            intercept = sum_wy / sum_w
        else:
            slope = (sum_w * sum_wxy - sum_wx * sum_wy) / denominator
            intercept = (sum_wy * sum_wxx - sum_wx * sum_wxy) / denominator
        start_day = min(x_values)
        end_day = max(x_values)
        return slope, intercept, start_day, end_day

    def get_trend_line_values(self, line_params, day_indices):
        """
        根据趋势线参数计算指定天数的趋势线值
        """
        if line_params is None:
            return [None] * len(day_indices)
        slope, intercept, _, _ = line_params
        return [slope * day + intercept for day in day_indices]

    def get_turn_points_info(self):
        """
        获取转折点信息
        """
        return self.turn_point_detector.get_peaks(), self.turn_point_detector.get_troughs()

    def get_uptrend_troughs(self):
        """
        获取上升趋势中的波谷
        """
        if hasattr(self.turn_point_detector, 'get_uptrend_troughs'):
            return self.turn_point_detector.get_uptrend_troughs()
        else:
            # 对于原始检测器，简单筛选上升趋势中的波谷
            troughs = self.turn_point_detector.get_troughs()
            if len(troughs) < 2:
                return troughs

            uptrend_troughs = []
            for i in range(1, len(troughs)):
                if troughs[i].price > troughs[i-1].price:
                    uptrend_troughs.append(troughs[i])
            return uptrend_troughs

    def analyze_swing_amplitude(self):
        """
        分析震荡幅度
        """
        peaks = self.turn_point_detector.get_peaks()
        troughs = self.turn_point_detector.get_troughs()

        if len(peaks) == 0 or len(troughs) == 0:
            return []

        swings = []
        all_points = sorted(peaks + troughs, key=lambda x: x.day_index)

        for i in range(1, len(all_points)):
            prev_point = all_points[i-1]
            curr_point = all_points[i]

            if prev_point.point_type != curr_point.point_type:
                if curr_point.point_type == 'PEAK':
                    amplitude = (curr_point.price - prev_point.price) / prev_point.price * 100
                else:
                    amplitude = (prev_point.price - curr_point.price) / prev_point.price * 100

                swings.append({
                    'from': prev_point,
                    'to': curr_point,
                    'amplitude': amplitude,
                    'direction': 'up' if curr_point.point_type == 'PEAK' else 'down'
                })

        return swings

    def get_trading_signals(self):
        """
        获取交易信号
        """
        if self.trading_strategy is None:
            return []
        return self.trading_strategy.analyze_signals(self)

    def get_buy_signals(self):
        """获取买入信号"""
        if self.trading_strategy is None:
            return []
        return self.trading_strategy.get_signals_by_type('BUY')

    def get_sell_signals(self):
        """获取卖出信号"""
        if self.trading_strategy is None:
            return []
        return self.trading_strategy.get_signals_by_type('SELL')

    def get_strategy_performance(self):
        """获取策略表现"""
        if self.trading_strategy is None:
            return None
        return self.trading_strategy.calculate_performance()

def create_interactive_chart(data, window_size=60, turn_point_threshold=10.0, enable_trading=True):
    """
    创建交互式股票趋势分析页面
    """
    stock_data = []
    for i, record in enumerate(data):
        stock_data.append({
            'date': str(record['date']),
            'open': float(record['open']),
            'close': float(record['close']),
            'high': float(record['high']),
            'low': float(record['low']),
            'index': i
        })

    html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>增强型股票趋势分析</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .controls {{
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }}
        .btn {{
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }}
        .btn:hover {{
            background-color: #0056b3;
        }}
        .btn:disabled {{
            background-color: #6c757d;
            cursor: not-allowed;
        }}
        .info {{
            margin: 10px 0;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 3px;
            font-family: monospace;
        }}
        #chart {{
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }}
        .stats {{
            display: flex;
            justify-content: space-around;
            margin: 15px 0;
        }}
        .stat-item {{
            text-align: center;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }}
        .stat-value {{
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }}
        .stat-label {{
            font-size: 12px;
            color: #6c757d;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>📈 增强型股票趋势分析 (波动阈值: {turn_point_threshold}%)</h1>

        <div class="controls">
            <button class="btn" onclick="addNextDay()" id="nextBtn">添加下一个交易日</button>
            <button class="btn" onclick="resetChart()">重置图表</button>
            <button class="btn" onclick="autoPlay()" id="autoBtn">自动播放</button>
            <button class="btn" onclick="stopAuto()" id="stopBtn" disabled>停止播放</button>
        </div>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="currentDay">0</div>
                <div class="stat-label">当前交易日</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="totalDays">{len(stock_data)}</div>
                <div class="stat-label">总交易日</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="highSlope">--</div>
                <div class="stat-label">高点趋势斜率</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="lowSlope">--</div>
                <div class="stat-label">低点趋势斜率</div>
            </div>
        </div>

        <div class="info" id="info">
            点击"添加下一个交易日"开始分析，或点击"自动播放"观看完整过程
        </div>

        <div id="chart"></div>
    </div>

    <script>
        // 增强的转折点检测器 (JS版本)
        class EnhancedTurnPointDetector {{
            constructor(percentChangeThreshold = 10.0) {{
                this.threshold = percentChangeThreshold / 100.0;
                this.records = [];
                this.turn_points = [];
                this.last_extreme_price = null;
                this.last_extreme_point_info = null; // {{date, price, dayIndex}}
                this.current_trend = 0; // 0: undefined, 1: up, -1: down
                this.temp_first_point_high_info = null;
                this.temp_first_point_low_info = null;
            }}

            addRecord(date, high, low, dayIndex) {{
                this.records.push({{ date, high, low, dayIndex }});

                if (this.current_trend === 0) {{
                    if (!this.temp_first_point_high_info) {{
                        this.temp_first_point_high_info = {{ date, price: high, dayIndex }};
                        this.temp_first_point_low_info = {{ date, price: low, dayIndex }};
                    }} else {{
                        const firstHigh = this.temp_first_point_high_info.price;

                        if (high > firstHigh * (1 + this.threshold)) {{
                            this.current_trend = 1; // UP
                            this.turn_points.push({{
                                date: this.temp_first_point_low_info.date,
                                price: this.temp_first_point_low_info.price,
                                pointType: 'TROUGH',
                                dayIndex: this.temp_first_point_low_info.dayIndex
                            }});
                            this.last_extreme_price = high;
                            this.last_extreme_point_info = {{ date, price: high, dayIndex }};
                        }} else if (low < firstHigh * (1 - this.threshold)) {{
                            this.current_trend = -1; // DOWN
                            this.turn_points.push({{
                                date: this.temp_first_point_high_info.date,
                                price: this.temp_first_point_high_info.price,
                                pointType: 'PEAK',
                                dayIndex: this.temp_first_point_high_info.dayIndex
                            }});
                            this.last_extreme_price = low;
                            this.last_extreme_point_info = {{ date, price: low, dayIndex }};
                        }} else {{
                            if (high > this.temp_first_point_high_info.price) {{
                                this.temp_first_point_high_info = {{ date, price: high, dayIndex }};
                            }}
                            if (low < this.temp_first_point_low_info.price) {{
                                this.temp_first_point_low_info = {{ date, price: low, dayIndex }};
                            }}
                        }}
                    }}
                }} else if (this.current_trend === 1) {{ // UP trend
                    if (high > this.last_extreme_price) {{
                        this.last_extreme_price = high;
                        this.last_extreme_point_info = {{ date, price: high, dayIndex }};
                    }} else if (low < this.last_extreme_price * (1 - this.threshold)) {{
                        const peak = {{
                            date: this.last_extreme_point_info.date,
                            price: this.last_extreme_point_info.price,
                            pointType: 'PEAK',
                            dayIndex: this.last_extreme_point_info.dayIndex
                        }};
                        if (this.turn_points.length === 0 || peak.price > this.turn_points[this.turn_points.length - 1].price) {{
                            this.turn_points.push(peak);
                            this.current_trend = -1; // DOWN
                            this.last_extreme_price = low;
                            this.last_extreme_point_info = {{ date, price: low, dayIndex }};
                        }}
                    }}
                }} else if (this.current_trend === -1) {{ // DOWN trend
                    if (low < this.last_extreme_price) {{
                        this.last_extreme_price = low;
                        this.last_extreme_point_info = {{ date, price: low, dayIndex }};
                    }} else if (high > this.last_extreme_price * (1 + this.threshold)) {{
                        const trough = {{
                            date: this.last_extreme_point_info.date,
                            price: this.last_extreme_point_info.price,
                            pointType: 'TROUGH',
                            dayIndex: this.last_extreme_point_info.dayIndex
                        }};
                        if (this.turn_points.length === 0 || trough.price < this.turn_points[this.turn_points.length - 1].price) {{
                            this.turn_points.push(trough);
                            this.current_trend = 1; // UP
                            this.last_extreme_price = high;
                            this.last_extreme_point_info = {{ date, price: high, dayIndex }};
                        }}
                    }}
                }}
            }}
            getPeaks() {{ return this.turn_points.filter(p => p.pointType === 'PEAK'); }}
            getTroughs() {{ return this.turn_points.filter(t => t.pointType === 'TROUGH'); }}
        }}

        // 交易策略类（JavaScript版本）
        class SmartTradingStrategy {{
            constructor(buyThreshold = 8.0, sellThreshold = 15.0) {{
                this.buyThreshold = buyThreshold / 100.0;
                this.sellThreshold = sellThreshold / 100.0;
                this.signals = [];
            }}

            analyzeSignals(calculator) {{
                this.signals = [];
                const peaks = calculator.turnPointDetector.getPeaks();
                const troughs = calculator.turnPointDetector.getTroughs();

                // 生成买入信号 - 上升趋势中的波谷
                for (let trough of troughs) {{
                    if (this.isUptrendBuyOpportunity(trough, calculator)) {{
                        this.signals.push({{
                            date: trough.date,
                            price: trough.price,
                            signalType: 'BUY',
                            dayIndex: trough.dayIndex,
                            reason: '上升趋势波谷买入',
                            confidence: 0.8
                        }});
                    }}
                }}

                // 生成卖出信号 - 高位获利了结
                for (let peak of peaks) {{
                    if (this.isProfitTakingOpportunity(peak, calculator)) {{
                        this.signals.push({{
                            date: peak.date,
                            price: peak.price,
                            signalType: 'SELL',
                            dayIndex: peak.dayIndex,
                            reason: '高位获利了结',
                            confidence: 0.8
                        }});
                    }}
                }}

                return this.signals;
            }}

            isUptrendBuyOpportunity(trough, calculator) {{
                // 简化版：检查是否为上升趋势中的波谷
                const troughs = calculator.turnPointDetector.getTroughs();
                const troughIndex = troughs.findIndex(t => t.dayIndex === trough.dayIndex);
                if (troughIndex > 0) {{
                    return trough.price > troughs[troughIndex - 1].price;
                }}
                return false;
            }}

            isProfitTakingOpportunity(peak, calculator) {{
                // 检查涨幅是否足够
                const troughs = calculator.turnPointDetector.getTroughs();
                let prevTrough = null;
                for (let trough of troughs.reverse()) {{
                    if (trough.dayIndex < peak.dayIndex) {{
                        prevTrough = trough;
                        break;
                    }}
                }}
                if (prevTrough) {{
                    const gainRatio = (peak.price - prevTrough.price) / prevTrough.price;
                    return gainRatio >= this.sellThreshold;
                }}
                return false;
            }}

            getBuySignals() {{ return this.signals.filter(s => s.signalType === 'BUY'); }}
            getSellSignals() {{ return this.signals.filter(s => s.signalType === 'SELL'); }}
        }}

        // 趋势线计算器类（JavaScript版本）
        class StockTrendCalculator {{
            constructor(windowSize, turnPointThreshold, enableTrading = true) {{
                this.windowSize = windowSize;
                this.records = [];
                this.dayCounter = 0;
                this.turnPointDetector = new EnhancedTurnPointDetector(turnPointThreshold);
                this.tradingStrategy = enableTrading ? new SmartTradingStrategy(8.0, 15.0) : null;
            }}

            addRecord(timestamp, high, low) {{
                const record = {{
                    date: timestamp,
                    high: high,
                    low: low,
                    dayIndex: this.dayCounter
                }};
                this.records.push(record);
                this.turnPointDetector.addRecord(timestamp, high, low, this.dayCounter);
                this.dayCounter++;
                if (this.records.length > this.windowSize) {{
                    this.records.shift();
                }}
            }}

            getCurrentTrendLines() {{
                const peaks = this.turnPointDetector.getPeaks();
                const troughs = this.turnPointDetector.getTroughs();
                const currentStartDay = Math.max(0, this.dayCounter - this.windowSize);
                const recentPeaks = peaks.filter(p => p.dayIndex >= currentStartDay);
                const recentTroughs = troughs.filter(t => t.dayIndex >= currentStartDay);
                
                let highLine = null;
                if (recentPeaks.length >= 2) {{
                    const peakPoints = recentPeaks.map(p => [p.dayIndex, p.price]);
                    highLine = this.fitWeightedLine(peakPoints);
                }}

                let lowLine = null;
                if (recentTroughs.length >= 2) {{
                    const troughPoints = recentTroughs.map(t => [t.dayIndex, t.price]);
                    lowLine = this.fitWeightedLine(troughPoints);
                }}
                return [highLine, lowLine];
            }}

            fitWeightedLine(points) {{
                if (points.length < 2) return null;
                const n = points.length;
                const xValues = points.map(p => p[0]);
                const yValues = points.map(p => p[1]);
                const decayFactor = 0.05;
                const weights = Array.from({{length: n}}, (_, i) => Math.exp(-(n - 1 - i) * decayFactor));
                const sumW = weights.reduce((a, b) => a + b, 0);
                const sumWx = weights.reduce((sum, w, i) => sum + w * xValues[i], 0);
                const sumWy = weights.reduce((sum, w, i) => sum + w * yValues[i], 0);
                const sumWxx = weights.reduce((sum, w, i) => sum + w * xValues[i] * xValues[i], 0);
                const sumWxy = weights.reduce((sum, w, i) => sum + w * xValues[i] * yValues[i], 0);
                const denominator = sumW * sumWxx - sumWx * sumWx;
                let slope, intercept;
                if (Math.abs(denominator) < 1e-10) {{
                    slope = 0;
                    intercept = sumWy / sumW;
                }} else {{
                    slope = (sumW * sumWxy - sumWx * sumWy) / denominator;
                    intercept = (sumWy * sumWxx - sumWx * sumWxy) / denominator;
                }}
                return {{
                    slope: slope,
                    intercept: intercept,
                    startDay: Math.min(...xValues),
                    endDay: Math.max(...xValues)
                }};
            }}

            getTrendLineValues(lineParams, dayIndices) {{
                if (!lineParams) return dayIndices.map(() => null);
                return dayIndices.map(day => lineParams.slope * day + lineParams.intercept);
            }}

            getTradingSignals() {{
                if (!this.tradingStrategy) return [];
                return this.tradingStrategy.analyzeSignals(this);
            }}

            getBuySignals() {{
                if (!this.tradingStrategy) return [];
                return this.tradingStrategy.getBuySignals();
            }}

            getSellSignals() {{
                if (!this.tradingStrategy) return [];
                return this.tradingStrategy.getSellSignals();
            }}
        }}

        // 股票数据和初始化
        const stockData = {json.dumps(stock_data, indent=2)};
        const windowSize = {window_size};
        const turnPointThreshold = {turn_point_threshold};

        // 当前状态
        let currentIndex = 0;
        let calculator = new StockTrendCalculator(windowSize, turnPointThreshold);
        let autoPlayInterval = null;

        // 图表实例
        let chart = echarts.init(document.getElementById('chart'));

        // 更新图表
        function updateChart() {{
            if (currentIndex === 0) {{
                chart.clear();
                return;
            }}

            const currentData = stockData.slice(0, currentIndex);
            const dates = currentData.map(d => d.date);
            const klineData = currentData.map(d => [d.open, d.close, d.low, d.high]);

            const [highLine, lowLine] = calculator.getCurrentTrendLines();
            const dayIndices = currentData.map((_, i) => i);

            let highTrends = calculator.getTrendLineValues(highLine, dayIndices);
            let lowTrends = calculator.getTrendLineValues(lowLine, dayIndices);
            let highSlope = highLine ? highLine.slope : 0;
            let lowSlope = lowLine ? lowLine.slope : 0;

            document.getElementById('currentDay').textContent = currentIndex;
            document.getElementById('highSlope').textContent = highSlope.toFixed(4);
            document.getElementById('lowSlope').textContent = lowSlope.toFixed(4);

            const peaks = calculator.turnPointDetector.getPeaks();
            const troughs = calculator.turnPointDetector.getTroughs();

            // 获取交易信号
            const tradingSignals = calculator.getTradingSignals();
            const buySignals = calculator.getBuySignals();
            const sellSignals = calculator.getSellSignals();

            const peakMarkers = peaks.map(p => ({{
                name: '波峰',
                coord: [stockData[p.dayIndex].date, p.price],
                value: p.price.toFixed(2),
                itemStyle: {{ color: 'rgb(234, 85, 69)' }}
            }}));
            const troughMarkers = troughs.map(t => ({{
                name: '波谷',
                coord: [stockData[t.dayIndex].date, t.price],
                value: t.price.toFixed(2),
                itemStyle: {{ color: 'rgb(69, 234, 135)' }}
            }}));

            // 醒目的买卖信号标记
            const buyMarkers = buySignals.map(b => ({{
                name: '买入信号',
                coord: [stockData[b.dayIndex].date, b.price],
                value: '买入\\n' + b.price.toFixed(2),
                symbol: 'triangle',
                symbolSize: 35,
                itemStyle: {{
                    color: '#00FF00',
                    borderColor: '#008000',
                    borderWidth: 3
                }},
                label: {{
                    show: true,
                    position: 'bottom',
                    fontSize: 12,
                    fontWeight: 'bold',
                    color: '#FFFFFF',
                    backgroundColor: '#00AA00',
                    padding: [4, 8],
                    borderRadius: 4
                }}
            }}));

            const sellMarkers = sellSignals.map(s => ({{
                name: '卖出信号',
                coord: [stockData[s.dayIndex].date, s.price],
                value: '卖出\\n' + s.price.toFixed(2),
                symbol: 'triangle',
                symbolSize: 35,
                symbolRotate: 180,
                itemStyle: {{
                    color: '#FF0000',
                    borderColor: '#800000',
                    borderWidth: 3
                }},
                label: {{
                    show: true,
                    position: 'top',
                    fontSize: 12,
                    fontWeight: 'bold',
                    color: '#FFFFFF',
                    backgroundColor: '#CC0000',
                    padding: [4, 8],
                    borderRadius: 4
                }}
            }}));

            const option = {{
                title: {{
                    text: `增强型股票趋势分析 (阈值: {turn_point_threshold}%)`,
                    left: 'center'
                }},
                tooltip: {{ trigger: 'axis', axisPointer: {{ type: 'cross' }} }},
                legend: {{ data: ['K线', '高点趋势线', '低点趋势线', '买入信号', '卖出信号'], top: 30 }},
                grid: {{ left: '3%', right: '4%', bottom: '15%', top: '15%', containLabel: true }},
                xAxis: {{ type: 'category', data: dates, scale: true, boundaryGap: false, axisLine: {{ onZero: false }}, splitLine: {{ show: false }}, min: 'dataMin', max: 'dataMax' }},
                yAxis: {{ scale: true, splitArea: {{ show: true }} }},
                dataZoom: [ {{ type: 'inside', start: 0, end: 100 }}, {{ show: true, type: 'slider', top: '90%', start: 0, end: 100 }} ],
                series: [
                    {{
                        name: 'K线',
                        type: 'candlestick',
                        data: klineData,
                        itemStyle: {{ color: '#ec0000', color0: '#00da3c', borderColor: '#8A0000', borderColor0: '#008F28' }},
                        markPoint: {{
                            symbol: 'pin',
                            symbolSize: 25,
                            label: {{ fontSize: 10, color: '#fff' }},
                            data: [...peakMarkers, ...troughMarkers]
                        }}
                    }},
                    {{ name: '高点趋势线', type: 'line', data: highTrends, smooth: false, symbol: 'none', lineStyle: {{ color: '#1E90FF', width: 2 }} }},
                    {{ name: '低点趋势线', type: 'line', data: lowTrends, smooth: false, symbol: 'none', lineStyle: {{ color: '#FF8C00', width: 2 }} }},
                    {{
                        name: '买入信号',
                        type: 'scatter',
                        data: buySignals.map(b => [stockData[b.dayIndex].date, b.price]),
                        symbol: 'triangle',
                        symbolSize: 20,
                        itemStyle: {{ color: '#00FF00', borderColor: '#008000', borderWidth: 2 }},
                        label: {{ show: false }},
                        emphasis: {{
                            label: {{
                                show: true,
                                formatter: '买入: {{c}}',
                                backgroundColor: '#00AA00',
                                color: '#FFFFFF',
                                padding: [4, 8],
                                borderRadius: 4
                            }}
                        }}
                    }},
                    {{
                        name: '卖出信号',
                        type: 'scatter',
                        data: sellSignals.map(s => [stockData[s.dayIndex].date, s.price]),
                        symbol: 'triangle',
                        symbolSize: 20,
                        symbolRotate: 180,
                        itemStyle: {{ color: '#FF0000', borderColor: '#800000', borderWidth: 2 }},
                        label: {{ show: false }},
                        emphasis: {{
                            label: {{
                                show: true,
                                formatter: '卖出: {{c}}',
                                backgroundColor: '#CC0000',
                                color: '#FFFFFF',
                                padding: [4, 8],
                                borderRadius: 4
                            }}
                        }}
                    }}
                ]
            }};
            chart.setOption(option);
        }}

        function addNextDay() {{
            if (currentIndex >= stockData.length) {{
                document.getElementById('info').textContent = '所有数据已添加完毕！';
                document.getElementById('nextBtn').disabled = true;
                return;
            }}
            const record = stockData[currentIndex];
            calculator.addRecord(record.date, record.high, record.low, record.close);
            currentIndex++;
            updateChart();
            const peaks = calculator.turnPointDetector.getPeaks();
            const troughs = calculator.turnPointDetector.getTroughs();
            const buySignals = calculator.getBuySignals();
            const sellSignals = calculator.getSellSignals();
            document.getElementById('info').textContent = `已添加第 ${{currentIndex}} 个交易日: ${{record.date}} | 波峰: ${{peaks.length}}个, 波谷: ${{troughs.length}}个 | 买入信号: ${{buySignals.length}}个, 卖出信号: ${{sellSignals.length}}个`;
            if (currentIndex >= stockData.length) {{
                document.getElementById('nextBtn').disabled = true;
                document.getElementById('info').textContent += ' - 所有数据已添加完毕！';
            }}
        }}

        function resetChart() {{
            currentIndex = 0;
            calculator = new StockTrendCalculator(windowSize, turnPointThreshold);
            chart.clear();
            document.getElementById('nextBtn').disabled = false;
            document.getElementById('currentDay').textContent = '0';
            document.getElementById('highSlope').textContent = '--';
            document.getElementById('lowSlope').textContent = '--';
            document.getElementById('info').textContent = '图表已重置，点击"添加下一个交易日"开始分析';
            stopAuto();
        }}

        function autoPlay() {{
            if (autoPlayInterval) return;
            document.getElementById('autoBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            autoPlayInterval = setInterval(() => {{
                if (currentIndex >= stockData.length) {{
                    stopAuto();
                    return;
                }}
                addNextDay();
            }}, 200); // 播放速度加快
        }}

        function stopAuto() {{
            if (autoPlayInterval) {{
                clearInterval(autoPlayInterval);
                autoPlayInterval = null;
            }}
            document.getElementById('autoBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
        }}

        updateChart();
    </script>
</body>
</html>
    """

    # 保存HTML文件
    filename = "interactive_stock_analysis.html"
    with open(filename, "w", encoding="utf-8") as f:
        f.write(html_content)
    
    print(f"交互式股票分析页面已保存为 {filename}")
    # 自动在浏览器中打开
    webbrowser.open('file://' + os.path.realpath(filename))

def visualize_stock_trends(data, window_size=60, turn_point_threshold=10.0, enable_trading=True):
    """
    创建交互式可视化
    """
    create_interactive_chart(data, window_size, turn_point_threshold, enable_trading)

def get_data(stock_code: str, start_date: str, end_date: str):
    """获取股票历史数据"""
    print(f"正在获取股票代码 {stock_code} 从 {start_date} 到 {end_date} 的数据...")
    df = ak.stock_zh_a_hist(
        symbol=stock_code,
        period="daily",
        start_date=start_date,
        end_date=end_date,
        adjust="qfq"  # 使用前复权数据
    )
    if df.empty:
        print("未能获取到数据，请检查股票代码和日期范围。")
        return []

    data = []
    for _, row in df.iterrows():
        data.append({
            'date': row['日期'],
            'open': row['开盘'],
            'close': row['收盘'],
            'high': row['最高'],
            'low': row['最低']
        })
    print(f"成功获取 {len(data)} 条数据。")
    return data

def analyze_stock_trends(data, window_size=60, turn_point_threshold=10.0, enable_trading=True):
    """
    分析股票趋势，重点关注上升趋势中的低点和震荡
    """
    print(f"\n=== 开始分析股票趋势 (阈值: {turn_point_threshold}%) ===")

    # 使用高级检测器和交易策略
    calculator = StockTrendCalculator(
        window_size=window_size,
        turn_point_threshold=turn_point_threshold,
        use_advanced_detector=True,
        enable_trading=enable_trading
    )

    # 逐条添加数据
    for record in data:
        calculator.add_record(
            timestamp=record['date'],
            high=record['high'],
            low=record['low'],
            close=record['close']
        )

    # 获取分析结果
    peaks, troughs = calculator.get_turn_points_info()
    uptrend_troughs = calculator.get_uptrend_troughs()
    swings = calculator.analyze_swing_amplitude()

    # 获取交易信号
    calculator.get_trading_signals()  # 触发信号分析
    buy_signals = calculator.get_buy_signals()
    sell_signals = calculator.get_sell_signals()
    performance = calculator.get_strategy_performance()

    print(f"\n转折点统计:")
    print(f"  总波峰数量: {len(peaks)}")
    print(f"  总波谷数量: {len(troughs)}")
    print(f"  上升趋势中的波谷: {len(uptrend_troughs)}")

    print(f"\n🔥 交易信号分析:")
    print(f"  买入信号数量: {len(buy_signals)}")
    print(f"  卖出信号数量: {len(sell_signals)}")

    if buy_signals:
        print(f"\n💰 买入信号详情:")
        for i, signal in enumerate(buy_signals, 1):
            print(f"  {i}. 日期: {signal.date}, 价格: {signal.price:.2f}, 原因: {signal.reason}")

    if sell_signals:
        print(f"\n💸 卖出信号详情:")
        for i, signal in enumerate(sell_signals, 1):
            print(f"  {i}. 日期: {signal.date}, 价格: {signal.price:.2f}, 原因: {signal.reason}")

    if performance and performance['total_trades'] > 0:
        print(f"\n📊 策略表现:")
        print(f"  总交易次数: {performance['total_trades']}")
        print(f"  胜率: {performance['win_rate']:.1f}%")
        print(f"  总收益率: {performance['total_return']:.1f}%")
        print(f"  平均收益率: {performance['avg_return']:.1f}%")

        print(f"\n📈 交易记录:")
        if performance['trades']:
            for i, trade in enumerate(performance['trades'], 1):
                # 兼容新旧两种交易记录格式
                if 'buy_date' in trade:
                    # 旧格式
                    profit_text = f"+{trade['profit_ratio']*100:.1f}%" if trade['profit_ratio'] > 0 else f"{trade['profit_ratio']*100:.1f}%"
                    print(f"  {i}. {trade['buy_date']} 买入 {trade['buy_price']:.2f} -> "
                          f"{trade['sell_date']} 卖出 {trade['sell_price']:.2f} ({profit_text})")
                else:
                    # 新格式 - 来自交易历史记录
                    if 'profit_ratio' in trade:
                        profit_text = f"+{trade['profit_ratio']*100:.1f}%" if trade['profit_ratio'] > 0 else f"{trade['profit_ratio']*100:.1f}%"
                        print(f"  {i}. 卖出交易: {trade['date']} @ {trade['price']:.2f} ({profit_text}) - {trade['reason']}")
        else:
            print("  暂无完成的交易记录")

    print(f"\n震荡分析 (>={turn_point_threshold}%的波动):")
    significant_swings = [s for s in swings if s['amplitude'] >= turn_point_threshold]
    for i, swing in enumerate(significant_swings, 1):
        direction_text = "上涨" if swing['direction'] == 'up' else "下跌"
        print(f"  {i}. {swing['from'].date} -> {swing['to'].date}: "
              f"{direction_text} {swing['amplitude']:.1f}% "
              f"({swing['from'].price:.2f} -> {swing['to'].price:.2f})")

    return calculator

def simulate_daily_trading(data, window_size=60, turn_point_threshold=10.0, show_details=True):
    """
    逐日模拟交易，去除未来数据依赖

    Args:
        data: 股票数据
        window_size: 窗口大小
        turn_point_threshold: 转折点阈值
        show_details: 是否显示详细过程

    Returns:
        calculator: 计算器实例
    """
    print(f"\n=== 开始逐日模拟交易 (无未来数据) ===")
    print(f"数据范围: {data[0]['date']} 到 {data[-1]['date']} ({len(data)} 天)")
    print(f"转折点阈值: {turn_point_threshold}%")

    # 创建计算器
    calculator = StockTrendCalculator(
        window_size=window_size,
        turn_point_threshold=turn_point_threshold,
        use_advanced_detector=True,
        enable_trading=True
    )

    strategy = calculator.trading_strategy
    print(f"初始资金: {strategy.cash:,.2f}")

    # 记录关键事件
    signal_events = []
    daily_values = []

    # 逐日处理数据
    for i, record in enumerate(data):
        # 记录处理前的信号数量
        prev_signals = len(strategy.daily_signals)

        # 添加当日数据（触发逐日分析）
        calculator.add_record(
            timestamp=record['date'],
            high=record['high'],
            low=record['low'],
            close=record['close']
        )

        # 记录当日组合价值
        daily_values.append({
            'date': record['date'],
            'day': i + 1,
            'price': record['close'],
            'portfolio_value': strategy.portfolio_value,
            'cash': strategy.cash,
            'position': strategy.position.copy() if strategy.position else None
        })

        # 检查是否有新信号
        current_signals = len(strategy.daily_signals)
        if current_signals > prev_signals:
            new_signals = strategy.daily_signals[prev_signals:]
            for signal in new_signals:
                signal_events.append({
                    'day': i + 1,
                    'date': record['date'],
                    'signal': signal,
                    'portfolio_value': strategy.portfolio_value
                })

                if show_details:
                    signal_type = "🟢买入" if signal.signal_type == 'BUY' else "🔴卖出"
                    print(f"第{i+1:3d}天 {record['date']}: {signal_type} @ {signal.price:.2f} - {signal.reason}")
                    if strategy.position:
                        pos = strategy.position
                        print(f"         持仓: {pos['quantity']}股 @ {pos['entry_price']:.2f}, 价值: {strategy.portfolio_value:,.0f}")
                    else:
                        print(f"         现金: {strategy.cash:,.0f}")

    # 显示最终结果
    performance = strategy.calculate_performance()
    print(f"\n=== 模拟交易结果 ===")
    print(f"交易天数: {len(data)} 天")
    print(f"价格变化: {data[0]['close']:.2f} -> {data[-1]['close']:.2f} ({(data[-1]['close']/data[0]['close']-1)*100:+.1f}%)")
    print(f"策略收益: {performance['total_return']:+.1f}%")
    print(f"完成交易: {performance['total_trades']} 笔")
    print(f"交易胜率: {performance['win_rate']:.1f}%")
    print(f"最大回撤: {performance['max_drawdown']:.1f}%")
    print(f"最终价值: {performance['portfolio_value']:,.2f}")

    if strategy.position:
        pos = strategy.position
        print(f"当前持仓: {pos['quantity']}股 @ {pos['entry_price']:.2f}")

    # 显示信号统计
    if signal_events:
        print(f"\n=== 交易信号统计 ===")
        buy_signals = [e for e in signal_events if e['signal'].signal_type == 'BUY']
        sell_signals = [e for e in signal_events if e['signal'].signal_type == 'SELL']
        print(f"总信号数: {len(signal_events)} (买入: {len(buy_signals)}, 卖出: {len(sell_signals)})")

        if not show_details and signal_events:
            print(f"关键信号:")
            for event in signal_events[:5]:  # 显示前5个信号
                signal_type = "🟢买入" if event['signal'].signal_type == 'BUY' else "🔴卖出"
                print(f"  第{event['day']}天: {signal_type} @ {event['signal'].price:.2f}")
            if len(signal_events) > 5:
                print(f"  ... 还有 {len(signal_events)-5} 个信号")

    return calculator, daily_values, signal_events

if __name__ == "__main__":
    # --- 参数配置 ---
    STOCK_CODE = '003021'  # 股票代码
    START_DATE = '20230101' # 开始日期
    END_DATE = '20240624'   # 结束日期
    WINDOW_SIZE = 60        # 趋势线计算窗口
    TURN_POINT_THRESHOLD = 10.0 # 转折点识别阈值 (%)

    # 运行模式选择
    SIMULATION_MODE = True  # True: 逐日模拟模式, False: 传统批量分析模式
    SHOW_SIMULATION_DETAILS = False  # 是否显示模拟过程详情

    # 获取数据
    sample_data = get_data(stock_code=STOCK_CODE, start_date=START_DATE, end_date=END_DATE)

    if sample_data:
        if SIMULATION_MODE:
            # 逐日模拟模式 - 去除未来数据依赖
            print("🚀 运行模式: 逐日模拟交易 (无未来数据)")
            calculator, daily_values, signal_events = simulate_daily_trading(
                sample_data,
                window_size=WINDOW_SIZE,
                turn_point_threshold=TURN_POINT_THRESHOLD,
                show_details=SHOW_SIMULATION_DETAILS
            )
        else:
            # 传统批量分析模式
            print("📊 运行模式: 传统批量分析")
            calculator = analyze_stock_trends(
                sample_data,
                window_size=WINDOW_SIZE,
                turn_point_threshold=TURN_POINT_THRESHOLD
            )

        # 生成可视化图表 (保持原有的醒目效果)
        print(f"\n正在生成交互式图表...")
        visualize_stock_trends(
            sample_data,
            window_size=WINDOW_SIZE,
            turn_point_threshold=TURN_POINT_THRESHOLD,
            enable_trading=True
        )